<template>
  <section class="min-h-screen w-full bg-rose-50 flex items-center justify-center p-6 relative">
    <FallingHearts
      :count="50"
      :min-size="25"
      :max-size="45"
      :speed="0.8"
      :opacity="0.7"
    />
    <div class="bg-white max-w-2xl w-full rounded-2xl shadow-md p-8 sm:p-10 relative z-10">
      <!-- Header -->
      <header class="text-center mb-8">
        <h1 class="text-4xl font-serif text-neutral-900">
          {{ props.title }}
        </h1>
        <p class="text-sm text-neutral-500 uppercase tracking-wide">
          {{ props.date }}
        </p>
      </header>

      <!-- Schedule Items -->
      <ul class="space-y-6">
        <ItineraryItem
          v-for="(item, idx) in itineraryItems"
          :key="idx"
          :time="item.time"
          :title="item.title"
          :description="item.description"
        />
      </ul>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ItineraryItem from './ItineraryItem.vue'
import FallingHearts from './FallingHearts.vue'

interface ItineraryItemData {
  time: string
  title: string
  description: string
}

interface ItineraryPageProps {
  title?: string
  date?: string
  items?: ItineraryItemData[]
}

const props = withDefaults(defineProps<ItineraryPageProps>(), {
  title: 'Title',
  date: 'Date',
  items: () => [],
})

const itineraryItems = computed(() =>
  props.items.length > 0 ? props.items : []
)

</script>
