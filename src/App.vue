<script setup lang="ts">
import ItineraryPage from './components/ItineraryPage.vue'
</script>

<template>
  <ItineraryPage
    title="Champaign Date ❤️"
    date="Sunday - September 1, 2025"
    :items="[
      {
        time: '9:30 AM',
        title: 'Drinks & Breakfast',
        description: 'Start the morning at Mad Goat Coffee with a refreshing Matcha with Cane Syrup. Afterward, stroll just a few steps to Suzu\'s <PERSON><PERSON> for a light dessert.'
      },
      {
        time: '11:00 AM',
        title: 'Leisurely Walk',
        description: 'Take a gentle walk around Downtown Champaign or through West Side Park to enjoy the morning air.'
      },
      {
        time: '12:30 PM',
        title: 'Lunch',
        description: 'Head to Red Lobster for a relaxed midday meal, with plenty of warm Cheddar Bay Biscuits to enjoy alongside your seafood.'
      },
      {
        time: '2:00 PM',
        title: 'Afternoon Activity',
        description: 'Spend the afternoon working together on a calming Peanuts-themed Paint-by-Numbers kit, creating something fun and low-stress.'
      },
      {
        time: '4:00 PM',
        title: 'Quiet Break',
        description: 'Take some downtime—perhaps at home or a cozy spot—enjoying tea, conversation, or a short rest.'
      },
      {
        time: '6:30 PM',
        title: 'Dinner',
        description: 'Enjoy a delicious sushi and sashimi dinner at Sakanaya.'
      },
      {
        time: '8:30 PM',
        title: 'Evening Entertainment',
        description: 'Settle in for a selection of refreshing shows.'
      }
    ]"
  />
</template>

<style scoped></style>
