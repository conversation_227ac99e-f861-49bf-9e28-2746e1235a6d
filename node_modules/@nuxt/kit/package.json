{"name": "@nuxt/kit", "version": "3.18.1", "repository": {"type": "git", "url": "git+https://github.com/nuxt/nuxt.git", "directory": "packages/kit"}, "homepage": "https://nuxt.com/docs/api/kit", "description": "Toolkit for authoring modules and interacting with Nuxt", "license": "MIT", "type": "module", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.mjs"}, "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"c12": "^3.2.0", "consola": "^3.4.2", "defu": "^6.1.4", "destr": "^2.0.5", "errx": "^0.1.0", "exsolve": "^1.0.7", "ignore": "^7.0.5", "jiti": "^2.5.1", "klona": "^2.0.6", "knitwork": "^1.2.0", "mlly": "^1.7.4", "ohash": "^2.0.11", "pathe": "^2.0.3", "pkg-types": "^2.2.0", "scule": "^1.3.0", "semver": "^7.7.2", "std-env": "^3.9.0", "tinyglobby": "^0.2.14", "ufo": "^1.6.1", "unctx": "^2.4.1", "unimport": "^5.2.0", "untyped": "^2.0.0"}, "devDependencies": {"@rspack/core": "1.4.11", "@types/lodash-es": "4.17.12", "@types/semver": "7.7.0", "hookable": "5.5.3", "lodash-es": "4.17.21", "nitropack": "2.12.4", "unbuild": "3.6.0", "vite": "7.0.6", "vitest": "3.2.4", "webpack": "5.101.0", "@nuxt/schema": "3.18.1"}, "engines": {"node": ">=18.12.0"}, "scripts": {"test:attw": "attw --pack"}}