{"name": "local-pkg", "type": "module", "version": "1.1.2", "description": "Get information on local packages.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu-collective/local-pkg#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu-collective/local-pkg.git"}, "bugs": {"url": "https://github.com/antfu-collective/local-pkg/issues"}, "keywords": ["package"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=14"}, "dependencies": {"mlly": "^1.7.4", "pkg-types": "^2.3.0", "quansync": "^0.2.11"}, "devDependencies": {"@antfu/eslint-config": "^5.2.1", "@antfu/ni": "^25.0.0", "@antfu/utils": "^9.2.0", "@types/chai": "^5.2.2", "@types/node": "^24.3.0", "bumpp": "^10.2.3", "chai": "^5.3.1", "eslint": "^9.33.0", "esno": "^4.8.0", "find-up-simple": "^1.0.1", "typescript": "^5.9.2", "unbuild": "^3.6.1", "unplugin-quansync": "^0.4.4", "vitest": "^3.2.4"}, "scripts": {"build": "unbuild", "lint": "eslint .", "release": "bumpp", "typecheck": "tsc --noEmit", "test": "vitest run && node ./test/cjs.cjs && node ./test/esm.mjs"}}