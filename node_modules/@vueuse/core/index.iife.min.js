(function(R,b,n){"use strict";function Ae(e,t,o){var l;let r;n.isRef(o)?r={evaluating:o}:r=o||{};const{lazy:i=!1,flush:s="pre",evaluating:a=void 0,shallow:u=!0,onError:f=(l=globalThis.reportError)!=null?l:b.noop}=r,c=n.shallowRef(!i),d=u?n.shallowRef(t):n.ref(t);let h=0;return n.watchEffect(async m=>{if(!c.value)return;h++;const v=h;let S=!1;a&&Promise.resolve().then(()=>{a.value=!0});try{const p=await e(y=>{m(()=>{a&&(a.value=!1),S||y()})});v===h&&(d.value=p)}catch(p){f(p)}finally{a&&v===h&&(a.value=!1),S=!0}},{flush:s}),i?n.computed(()=>(c.value=!0,d.value)):d}function Vt(e,t,o,l){let r=n.inject(e);return o&&(r=n.inject(e,o)),l&&(r=n.inject(e,o,l)),typeof t=="function"?n.computed(i=>t(r,i)):n.computed({get:i=>t.get(r,i),set:t.set})}function Ft(e={}){const{inheritAttrs:t=!0}=e,o=n.shallowRef(),l=n.defineComponent({setup(i,{slots:s}){return()=>{o.value=s.default}}}),r=n.defineComponent({inheritAttrs:t,props:e.props,setup(i,{attrs:s,slots:a}){return()=>{var u;if(!o.value&&process.env.NODE_ENV!=="production")throw new Error("[VueUse] Failed to find the definition of reusable template");const f=(u=o.value)==null?void 0:u.call(o,{...e.props==null?Pt(s):i,$slots:a});return t&&f?.length===1?f[0]:f}}});return b.makeDestructurable({define:l,reuse:r},[l,r])}function Pt(e){const t={};for(const o in e)t[b.camelize(o)]=e[o];return t}function Dt(e={}){let t=0;const o=n.ref([]);function l(...s){const a=n.shallowReactive({key:t++,args:s,promise:void 0,resolve:()=>{},reject:()=>{},isResolving:!1,options:e});return o.value.push(a),a.promise=new Promise((u,f)=>{a.resolve=c=>(a.isResolving=!0,u(c)),a.reject=f}).finally(()=>{a.promise=void 0;const u=o.value.indexOf(a);u!==-1&&o.value.splice(u,1)}),a.promise}function r(...s){return e.singleton&&o.value.length>0?o.value[0].promise:l(...s)}const i=n.defineComponent((s,{slots:a})=>{const u=()=>o.value.map(f=>{var c;return n.h(n.Fragment,{key:f.key},(c=a.default)==null?void 0:c.call(a,f))});return e.transition?()=>n.h(n.TransitionGroup,e.transition,u):u});return i.start=r,i}function Ct(e){return function(...t){return e.apply(this,t.map(o=>n.toValue(o)))}}const L=b.isClient?window:void 0,q=b.isClient?window.document:void 0,G=b.isClient?window.navigator:void 0,At=b.isClient?window.location:void 0;function W(e){var t;const o=n.toValue(e);return(t=o?.$el)!=null?t:o}function k(...e){const t=[],o=()=>{t.forEach(a=>a()),t.length=0},l=(a,u,f,c)=>(a.addEventListener(u,f,c),()=>a.removeEventListener(u,f,c)),r=n.computed(()=>{const a=b.toArray(n.toValue(e[0])).filter(u=>u!=null);return a.every(u=>typeof u!="string")?a:void 0}),i=b.watchImmediate(()=>{var a,u;return[(u=(a=r.value)==null?void 0:a.map(f=>W(f)))!=null?u:[L].filter(f=>f!=null),b.toArray(n.toValue(r.value?e[1]:e[0])),b.toArray(n.unref(r.value?e[2]:e[1])),n.toValue(r.value?e[3]:e[2])]},([a,u,f,c])=>{if(o(),!a?.length||!u?.length||!f?.length)return;const d=b.isObject(c)?{...c}:c;t.push(...a.flatMap(h=>u.flatMap(m=>f.map(v=>l(h,m,v,d)))))},{flush:"post"}),s=()=>{i(),o()};return b.tryOnScopeDispose(o),s}let Me=!1;function Mt(e,t,o={}){const{window:l=L,ignore:r=[],capture:i=!0,detectIframe:s=!1,controls:a=!1}=o;if(!l)return a?{stop:b.noop,cancel:b.noop,trigger:b.noop}:b.noop;if(b.isIOS&&!Me){Me=!0;const p={passive:!0};Array.from(l.document.body.children).forEach(y=>y.addEventListener("click",b.noop,p)),l.document.documentElement.addEventListener("click",b.noop,p)}let u=!0;const f=p=>n.toValue(r).some(y=>{if(typeof y=="string")return Array.from(l.document.querySelectorAll(y)).some(w=>w===p.target||p.composedPath().includes(w));{const w=W(y);return w&&(p.target===w||p.composedPath().includes(w))}});function c(p){const y=n.toValue(p);return y&&y.$.subTree.shapeFlag===16}function d(p,y){const w=n.toValue(p),g=w.$.subTree&&w.$.subTree.children;return g==null||!Array.isArray(g)?!1:g.some(O=>O.el===y.target||y.composedPath().includes(O.el))}const h=p=>{const y=W(e);if(p.target!=null&&!(!(y instanceof Element)&&c(e)&&d(e,p))&&!(!y||y===p.target||p.composedPath().includes(y))){if("detail"in p&&p.detail===0&&(u=!f(p)),!u){u=!0;return}t(p)}};let m=!1;const v=[k(l,"click",p=>{m||(m=!0,setTimeout(()=>{m=!1},0),h(p))},{passive:!0,capture:i}),k(l,"pointerdown",p=>{const y=W(e);u=!f(p)&&!!(y&&!p.composedPath().includes(y))},{passive:!0}),s&&k(l,"blur",p=>{setTimeout(()=>{var y;const w=W(e);((y=l.document.activeElement)==null?void 0:y.tagName)==="IFRAME"&&!w?.contains(l.document.activeElement)&&t(p)},0)},{passive:!0})].filter(Boolean),S=()=>v.forEach(p=>p());return a?{stop:S,cancel:()=>{u=!1},trigger:p=>{u=!0,h(p),u=!1}}:S}function Ie(){const e=n.shallowRef(!1),t=n.getCurrentInstance();return t&&n.onMounted(()=>{e.value=!0},t),e}function H(e){const t=Ie();return n.computed(()=>(t.value,!!e()))}function X(e,t,o={}){const{window:l=L,...r}=o;let i;const s=H(()=>l&&"MutationObserver"in l),a=()=>{i&&(i.disconnect(),i=void 0)},u=n.computed(()=>{const h=n.toValue(e),m=b.toArray(h).map(W).filter(b.notNullish);return new Set(m)}),f=n.watch(u,h=>{a(),s.value&&h.size&&(i=new MutationObserver(t),h.forEach(m=>i.observe(m,r)))},{immediate:!0,flush:"post"}),c=()=>i?.takeRecords(),d=()=>{f(),a()};return b.tryOnScopeDispose(d),{isSupported:s,stop:d,takeRecords:c}}function we(e,t,o={}){const{window:l=L,document:r=l?.document,flush:i="sync"}=o;if(!l||!r)return b.noop;let s;const a=c=>{s?.(),s=c},u=n.watchEffect(()=>{const c=W(e);if(c){const{stop:d}=X(r,h=>{h.map(v=>[...v.removedNodes]).flat().some(v=>v===c||v.contains(c))&&t(h)},{window:l,childList:!0,subtree:!0});a(d)}},{flush:i}),f=()=>{u(),a()};return b.tryOnScopeDispose(f),f}function It(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function se(...e){let t,o,l={};e.length===3?(t=e[0],o=e[1],l=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,o=e[0],l=e[1]):(t=e[0],o=e[1]):(t=!0,o=e[0]);const{target:r=L,eventName:i="keydown",passive:s=!1,dedupe:a=!1}=l,u=It(t);return k(r,i,c=>{c.repeat&&n.toValue(a)||u(c)&&o(c)},s)}function Lt(e,t,o={}){return se(e,t,{...o,eventName:"keydown"})}function Nt(e,t,o={}){return se(e,t,{...o,eventName:"keypress"})}function xt(e,t,o={}){return se(e,t,{...o,eventName:"keyup"})}const Wt=500,Ht=10;function Ut(e,t,o){var l,r;const i=n.computed(()=>W(e));let s,a,u,f=!1;function c(){s&&(clearTimeout(s),s=void 0),a=void 0,u=void 0,f=!1}function d(y){var w,g,O;const[E,P,T]=[u,a,f];if(c(),!o?.onMouseUp||!P||!E||(w=o?.modifiers)!=null&&w.self&&y.target!==i.value)return;(g=o?.modifiers)!=null&&g.prevent&&y.preventDefault(),(O=o?.modifiers)!=null&&O.stop&&y.stopPropagation();const _=y.x-P.x,V=y.y-P.y,C=Math.sqrt(_*_+V*V);o.onMouseUp(y.timeStamp-E,C,T)}function h(y){var w,g,O,E;(w=o?.modifiers)!=null&&w.self&&y.target!==i.value||(c(),(g=o?.modifiers)!=null&&g.prevent&&y.preventDefault(),(O=o?.modifiers)!=null&&O.stop&&y.stopPropagation(),a={x:y.x,y:y.y},u=y.timeStamp,s=setTimeout(()=>{f=!0,t(y)},(E=o?.delay)!=null?E:Wt))}function m(y){var w,g,O,E;if((w=o?.modifiers)!=null&&w.self&&y.target!==i.value||!a||o?.distanceThreshold===!1)return;(g=o?.modifiers)!=null&&g.prevent&&y.preventDefault(),(O=o?.modifiers)!=null&&O.stop&&y.stopPropagation();const P=y.x-a.x,T=y.y-a.y;Math.sqrt(P*P+T*T)>=((E=o?.distanceThreshold)!=null?E:Ht)&&c()}const v={capture:(l=o?.modifiers)==null?void 0:l.capture,once:(r=o?.modifiers)==null?void 0:r.once},S=[k(i,"pointerdown",h,v),k(i,"pointermove",m,v),k(i,["pointerup","pointerleave"],d,v)];return()=>S.forEach(y=>y())}function $t(){const{activeElement:e,body:t}=document;if(!e||e===t)return!1;switch(e.tagName){case"INPUT":case"TEXTAREA":return!0}return e.hasAttribute("contenteditable")}function Bt({keyCode:e,metaKey:t,ctrlKey:o,altKey:l}){return t||o||l?!1:e>=48&&e<=57||e>=96&&e<=105||e>=65&&e<=90}function jt(e,t={}){const{document:o=q}=t;o&&k(o,"keydown",r=>{!$t()&&Bt(r)&&e(r)},{passive:!0})}function zt(e,t=null){const o=n.getCurrentInstance();let l=()=>{};const r=n.customRef((i,s)=>(l=s,{get(){var a,u;return i(),(u=(a=o?.proxy)==null?void 0:a.$refs[e])!=null?u:t},set(){}}));return b.tryOnMounted(l),n.onUpdated(l),r}function Le(e={}){var t;const{window:o=L,deep:l=!0,triggerOnRemoval:r=!1}=e,i=(t=e.document)!=null?t:o?.document,s=()=>{var f;let c=i?.activeElement;if(l)for(;c?.shadowRoot;)c=(f=c?.shadowRoot)==null?void 0:f.activeElement;return c},a=n.shallowRef(),u=()=>{a.value=s()};if(o){const f={capture:!0,passive:!0};k(o,"blur",c=>{c.relatedTarget===null&&u()},f),k(o,"focus",u,f)}return r&&we(a,u,{document:i}),u(),a}function K(e,t={}){const{immediate:o=!0,fpsLimit:l=void 0,window:r=L,once:i=!1}=t,s=n.shallowRef(!1),a=n.computed(()=>l?1e3/n.toValue(l):null);let u=0,f=null;function c(m){if(!s.value||!r)return;u||(u=m);const v=m-u;if(a.value&&v<a.value){f=r.requestAnimationFrame(c);return}if(u=m,e({delta:v,timestamp:m}),i){s.value=!1,f=null;return}f=r.requestAnimationFrame(c)}function d(){!s.value&&r&&(s.value=!0,u=0,f=r.requestAnimationFrame(c))}function h(){s.value=!1,f!=null&&r&&(r.cancelAnimationFrame(f),f=null)}return o&&d(),b.tryOnScopeDispose(h),{isActive:n.readonly(s),pause:h,resume:d}}function qt(e,t,o){let l,r;b.isObject(o)?(l=o,r=b.objectOmit(o,["window","immediate","commitStyles","persist","onReady","onError"])):(l={duration:o},r=o);const{window:i=L,immediate:s=!0,commitStyles:a,persist:u,playbackRate:f=1,onReady:c,onError:d=I=>{console.error(I)}}=l,h=H(()=>i&&HTMLElement&&"animate"in HTMLElement.prototype),m=n.shallowRef(void 0),v=n.shallowReactive({startTime:null,currentTime:null,timeline:null,playbackRate:f,pending:!1,playState:s?"idle":"paused",replaceState:"active"}),S=n.computed(()=>v.pending),p=n.computed(()=>v.playState),y=n.computed(()=>v.replaceState),w=n.computed({get(){return v.startTime},set(I){v.startTime=I,m.value&&(m.value.startTime=I)}}),g=n.computed({get(){return v.currentTime},set(I){v.currentTime=I,m.value&&(m.value.currentTime=I,N())}}),O=n.computed({get(){return v.timeline},set(I){v.timeline=I,m.value&&(m.value.timeline=I)}}),E=n.computed({get(){return v.playbackRate},set(I){v.playbackRate=I,m.value&&(m.value.playbackRate=I)}}),P=()=>{if(m.value)try{m.value.play(),N()}catch(I){x(),d(I)}else M()},T=()=>{var I;try{(I=m.value)==null||I.pause(),x()}catch(U){d(U)}},_=()=>{var I;m.value||M();try{(I=m.value)==null||I.reverse(),N()}catch(U){x(),d(U)}},V=()=>{var I;try{(I=m.value)==null||I.finish(),x()}catch(U){d(U)}},C=()=>{var I;try{(I=m.value)==null||I.cancel(),x()}catch(U){d(U)}};n.watch(()=>W(e),I=>{I?M(!0):m.value=void 0}),n.watch(()=>t,I=>{if(m.value){M();const U=W(e);U&&(m.value.effect=new KeyframeEffect(U,n.toValue(I),r))}},{deep:!0}),b.tryOnMounted(()=>M(!0),!1),b.tryOnScopeDispose(C);function M(I){const U=W(e);!h.value||!U||(m.value||(m.value=U.animate(n.toValue(t),r)),u&&m.value.persist(),f!==1&&(m.value.playbackRate=f),I&&!s?m.value.pause():N(),c?.(m.value))}const F={passive:!0};k(m,["cancel","finish","remove"],x,F),k(m,"finish",()=>{var I;a&&((I=m.value)==null||I.commitStyles())},F);const{resume:A,pause:D}=K(()=>{m.value&&(v.pending=m.value.pending,v.playState=m.value.playState,v.replaceState=m.value.replaceState,v.startTime=m.value.startTime,v.currentTime=m.value.currentTime,v.timeline=m.value.timeline,v.playbackRate=m.value.playbackRate)},{immediate:!1});function N(){h.value&&A()}function x(){h.value&&i&&i.requestAnimationFrame(D)}return{isSupported:h,animate:m,play:P,pause:T,reverse:_,finish:V,cancel:C,pending:S,playState:p,replaceState:y,startTime:w,currentTime:g,timeline:O,playbackRate:E}}function Gt(e,t){const{interrupt:o=!0,onError:l=b.noop,onFinished:r=b.noop,signal:i}=t||{},s={aborted:"aborted",fulfilled:"fulfilled",pending:"pending",rejected:"rejected"},a=Array.from(Array.from({length:e.length}),()=>({state:s.pending,data:null})),u=n.reactive(a),f=n.shallowRef(-1);if(!e||e.length===0)return r(),{activeIndex:f,result:u};function c(d,h){f.value++,u[f.value].data=h,u[f.value].state=d}return e.reduce((d,h)=>d.then(m=>{var v;if(i?.aborted){c(s.aborted,new Error("aborted"));return}if(((v=u[f.value])==null?void 0:v.state)===s.rejected&&o){r();return}const S=h(m).then(p=>(c(s.fulfilled,p),f.value===e.length-1&&r(),p));return i?Promise.race([S,Yt(i)]):S}).catch(m=>i?.aborted?(c(s.aborted,m),m):(c(s.rejected,m),l(),m)),Promise.resolve()),{activeIndex:f,result:u}}function Yt(e){return new Promise((t,o)=>{const l=new Error("aborted");e.aborted?o(l):e.addEventListener("abort",()=>o(l),{once:!0})})}function Ne(e,t,o){var l;const{immediate:r=!0,delay:i=0,onError:s=(l=globalThis.reportError)!=null?l:b.noop,onSuccess:a=b.noop,resetOnExecute:u=!0,shallow:f=!0,throwError:c}=o??{},d=f?n.shallowRef(t):n.ref(t),h=n.shallowRef(!1),m=n.shallowRef(!1),v=n.shallowRef(void 0);async function S(w=0,...g){u&&(d.value=t),v.value=void 0,h.value=!1,m.value=!0,w>0&&await b.promiseTimeout(w);const O=typeof e=="function"?e(...g):e;try{const E=await O;d.value=E,h.value=!0,a(E)}catch(E){if(v.value=E,s(E),c)throw E}finally{m.value=!1}return d.value}r&&S(i);const p={state:d,isReady:h,isLoading:m,error:v,execute:S,executeImmediate:(...w)=>S(0,...w)};function y(){return new Promise((w,g)=>{b.until(m).toBe(!1).then(()=>w(p)).catch(g)})}return{...p,then(w,g){return y().then(w,g)}}}const te={array:e=>JSON.stringify(e),object:e=>JSON.stringify(e),set:e=>JSON.stringify(Array.from(e)),map:e=>JSON.stringify(Object.fromEntries(e)),null:()=>""};function Xt(e){return e?e instanceof Map?te.map:e instanceof Set?te.set:Array.isArray(e)?te.array:te.object:te.null}function Kt(e,t){const o=n.shallowRef(""),l=n.shallowRef();function r(){if(b.isClient)return l.value=new Promise((i,s)=>{try{const a=n.toValue(e);if(a==null)i("");else if(typeof a=="string")i(ge(new Blob([a],{type:"text/plain"})));else if(a instanceof Blob)i(ge(a));else if(a instanceof ArrayBuffer)i(window.btoa(String.fromCharCode(...new Uint8Array(a))));else if(a instanceof HTMLCanvasElement)i(a.toDataURL(t?.type,t?.quality));else if(a instanceof HTMLImageElement){const u=a.cloneNode(!1);u.crossOrigin="Anonymous",Jt(u).then(()=>{const f=document.createElement("canvas"),c=f.getContext("2d");f.width=u.width,f.height=u.height,c.drawImage(u,0,0,f.width,f.height),i(f.toDataURL(t?.type,t?.quality))}).catch(s)}else if(typeof a=="object"){const f=(t?.serializer||Xt(a))(a);return i(ge(new Blob([f],{type:"application/json"})))}else s(new Error("target is unsupported types"))}catch(a){s(a)}}),l.value.then(i=>{o.value=t?.dataUrl===!1?i.replace(/^data:.*?;base64,/,""):i}),l.value}return n.isRef(e)||typeof e=="function"?n.watch(e,r,{immediate:!0}):r(),{base64:o,promise:l,execute:r}}function Jt(e){return new Promise((t,o)=>{e.complete?t():(e.onload=()=>{t()},e.onerror=o)})}function ge(e){return new Promise((t,o)=>{const l=new FileReader;l.onload=r=>{t(r.target.result)},l.onerror=o,l.readAsDataURL(e)})}function Qt(e={}){const{navigator:t=G}=e,o=["chargingchange","chargingtimechange","dischargingtimechange","levelchange"],l=H(()=>t&&"getBattery"in t&&typeof t.getBattery=="function"),r=n.shallowRef(!1),i=n.shallowRef(0),s=n.shallowRef(0),a=n.shallowRef(1);let u;function f(){r.value=this.charging,i.value=this.chargingTime||0,s.value=this.dischargingTime||0,a.value=this.level}return l.value&&t.getBattery().then(c=>{u=c,f.call(u),k(u,o,f,{passive:!0})}),{isSupported:l,charging:r,chargingTime:i,dischargingTime:s,level:a}}function Zt(e){let{acceptAllDevices:t=!1}=e||{};const{filters:o=void 0,optionalServices:l=void 0,navigator:r=G}=e||{},i=H(()=>r&&"bluetooth"in r),s=n.shallowRef(),a=n.shallowRef(null);n.watch(s,()=>{h()});async function u(){if(i.value){a.value=null,o&&o.length>0&&(t=!1);try{s.value=await r?.bluetooth.requestDevice({acceptAllDevices:t,filters:o,optionalServices:l})}catch(m){a.value=m}}}const f=n.shallowRef(),c=n.shallowRef(!1);function d(){c.value=!1,s.value=void 0,f.value=void 0}async function h(){if(a.value=null,s.value&&s.value.gatt){k(s,"gattserverdisconnected",d,{passive:!0});try{f.value=await s.value.gatt.connect(),c.value=f.value.connected}catch(m){a.value=m}}}return b.tryOnMounted(()=>{var m;s.value&&((m=s.value.gatt)==null||m.connect())}),b.tryOnScopeDispose(()=>{var m;s.value&&((m=s.value.gatt)==null||m.disconnect())}),{isSupported:i,isConnected:n.readonly(c),device:s,requestDevice:u,server:f,error:a}}const be=Symbol("vueuse-ssr-width");function Se(){const e=n.hasInjectionContext()?b.injectLocal(be,null):null;return typeof e=="number"?e:void 0}function en(e,t){t!==void 0?t.provide(be,e):b.provideLocal(be,e)}function $(e,t={}){const{window:o=L,ssrWidth:l=Se()}=t,r=H(()=>o&&"matchMedia"in o&&typeof o.matchMedia=="function"),i=n.shallowRef(typeof l=="number"),s=n.shallowRef(),a=n.shallowRef(!1),u=f=>{a.value=f.matches};return n.watchEffect(()=>{if(i.value){i.value=!r.value;const f=n.toValue(e).split(",");a.value=f.some(c=>{const d=c.includes("not all"),h=c.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),m=c.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let v=!!(h||m);return h&&v&&(v=l>=b.pxValue(h[1])),m&&v&&(v=l<=b.pxValue(m[1])),d?!v:v});return}r.value&&(s.value=o.matchMedia(n.toValue(e)),a.value=s.value.matches)}),k(s,"change",u,{passive:!0}),n.computed(()=>a.value)}const tn={sm:640,md:768,lg:1024,xl:1280,"2xl":1536},nn={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400},xe={xs:0,sm:600,md:960,lg:1264,xl:1904},on={xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560},ln=xe,an={xs:480,sm:576,md:768,lg:992,xl:1200,xxl:1600},rn={xs:0,sm:600,md:1024,lg:1440,xl:1920},sn={mobileS:320,mobileM:375,mobileL:425,tablet:768,laptop:1024,laptopL:1440,desktop4K:2560},un={"3xs":360,"2xs":480,xs:600,sm:768,md:1024,lg:1280,xl:1440,"2xl":1600,"3xl":1920,"4xl":2560},cn={sm:576,md:768,lg:992,xl:1200},fn={xs:0,sm:768,md:992,lg:1200,xl:1920};function dn(e,t={}){function o(m,v){let S=n.toValue(e[n.toValue(m)]);return v!=null&&(S=b.increaseWithUnit(S,v)),typeof S=="number"&&(S=`${S}px`),S}const{window:l=L,strategy:r="min-width",ssrWidth:i=Se()}=t,s=typeof i=="number",a=s?n.shallowRef(!1):{value:!0};s&&b.tryOnMounted(()=>a.value=!!l);function u(m,v){return!a.value&&s?m==="min"?i>=b.pxValue(v):i<=b.pxValue(v):l?l.matchMedia(`(${m}-width: ${v})`).matches:!1}const f=m=>$(()=>`(min-width: ${o(m)})`,t),c=m=>$(()=>`(max-width: ${o(m)})`,t),d=Object.keys(e).reduce((m,v)=>(Object.defineProperty(m,v,{get:()=>r==="min-width"?f(v):c(v),enumerable:!0,configurable:!0}),m),{});function h(){const m=Object.keys(e).map(v=>[v,d[v],b.pxValue(o(v))]).sort((v,S)=>v[2]-S[2]);return n.computed(()=>m.filter(([,v])=>v.value).map(([v])=>v))}return Object.assign(d,{greaterOrEqual:f,smallerOrEqual:c,greater(m){return $(()=>`(min-width: ${o(m,.1)})`,t)},smaller(m){return $(()=>`(max-width: ${o(m,-.1)})`,t)},between(m,v){return $(()=>`(min-width: ${o(m)}) and (max-width: ${o(v,-.1)})`,t)},isGreater(m){return u("min",o(m,.1))},isGreaterOrEqual(m){return u("min",o(m))},isSmaller(m){return u("max",o(m,-.1))},isSmallerOrEqual(m){return u("max",o(m))},isInBetween(m,v){return u("min",o(m))&&u("max",o(v,-.1))},current:h,active(){const m=h();return n.computed(()=>m.value.length===0?"":m.value.at(r==="min-width"?-1:0))}})}function mn(e){const{name:t,window:o=L}=e,l=H(()=>o&&"BroadcastChannel"in o),r=n.shallowRef(!1),i=n.ref(),s=n.ref(),a=n.shallowRef(null),u=c=>{i.value&&i.value.postMessage(c)},f=()=>{i.value&&i.value.close(),r.value=!0};return l.value&&b.tryOnMounted(()=>{a.value=null,i.value=new BroadcastChannel(t);const c={passive:!0};k(i,"message",d=>{s.value=d.data},c),k(i,"messageerror",d=>{a.value=d},c),k(i,"close",()=>{r.value=!0},c)}),b.tryOnScopeDispose(()=>{f()}),{isSupported:l,channel:i,data:s,post:u,close:f,error:a,isClosed:r}}const We=["hash","host","hostname","href","pathname","port","protocol","search"];function vn(e={}){const{window:t=L}=e,o=Object.fromEntries(We.map(i=>[i,n.ref()]));for(const[i,s]of b.objectEntries(o))n.watch(s,a=>{!t?.location||t.location[i]===a||(t.location[i]=a)});const l=i=>{var s;const{state:a,length:u}=t?.history||{},{origin:f}=t?.location||{};for(const c of We)o[c].value=(s=t?.location)==null?void 0:s[c];return n.reactive({trigger:i,state:a,length:u,origin:f,...o})},r=n.ref(l("load"));if(t){const i={passive:!0};k(t,"popstate",()=>r.value=l("popstate"),i),k(t,"hashchange",()=>r.value=l("hashchange"),i)}return r}function pn(e,t=(l,r)=>l===r,o){const{deepRefs:l=!0,...r}=o||{},i=b.createRef(e.value,l);return n.watch(()=>e.value,s=>{t(s,i.value)||(i.value=s)},r),i}function ue(e,t={}){const{controls:o=!1,navigator:l=G}=t,r=H(()=>l&&"permissions"in l),i=n.shallowRef(),s=typeof e=="string"?{name:e}:e,a=n.shallowRef(),u=()=>{var c,d;a.value=(d=(c=i.value)==null?void 0:c.state)!=null?d:"prompt"};k(i,"change",u,{passive:!0});const f=b.createSingletonPromise(async()=>{if(r.value){if(!i.value)try{i.value=await l.permissions.query(s)}catch{i.value=void 0}finally{u()}if(o)return n.toRaw(i.value)}});return f(),o?{state:a,isSupported:r,query:f}:a}function hn(e={}){const{navigator:t=G,read:o=!1,source:l,copiedDuring:r=1500,legacy:i=!1}=e,s=H(()=>t&&"clipboard"in t),a=ue("clipboard-read"),u=ue("clipboard-write"),f=n.computed(()=>s.value||i),c=n.shallowRef(""),d=n.shallowRef(!1),h=b.useTimeoutFn(()=>d.value=!1,r,{immediate:!1});async function m(){let w=!(s.value&&y(a.value));if(!w)try{c.value=await t.clipboard.readText()}catch{w=!0}w&&(c.value=p())}f.value&&o&&k(["copy","cut"],m,{passive:!0});async function v(w=n.toValue(l)){if(f.value&&w!=null){let g=!(s.value&&y(u.value));if(!g)try{await t.clipboard.writeText(w)}catch{g=!0}g&&S(w),c.value=w,d.value=!0,h.start()}}function S(w){const g=document.createElement("textarea");g.value=w??"",g.style.position="absolute",g.style.opacity="0",document.body.appendChild(g),g.select(),document.execCommand("copy"),g.remove()}function p(){var w,g,O;return(O=(g=(w=document?.getSelection)==null?void 0:w.call(document))==null?void 0:g.toString())!=null?O:""}function y(w){return w==="granted"||w==="prompt"}return{isSupported:f,text:c,copied:d,copy:v}}function yn(e={}){const{navigator:t=G,read:o=!1,source:l,copiedDuring:r=1500}=e,i=H(()=>t&&"clipboard"in t),s=n.ref([]),a=n.shallowRef(!1),u=b.useTimeoutFn(()=>a.value=!1,r,{immediate:!1});function f(){i.value&&t.clipboard.read().then(d=>{s.value=d})}i.value&&o&&k(["copy","cut"],f,{passive:!0});async function c(d=n.toValue(l)){i.value&&d!=null&&(await t.clipboard.write(d),s.value=d,a.value=!0,u.start())}return{isSupported:i,content:n.shallowReadonly(s),copied:n.readonly(a),copy:c,read:f}}function ne(e){return JSON.parse(JSON.stringify(e))}function wn(e,t={}){const o=n.ref({}),l=n.shallowRef(!1);let r=!1;const{manual:i,clone:s=ne,deep:a=!0,immediate:u=!0}=t;n.watch(o,()=>{if(r){r=!1;return}l.value=!0},{deep:!0,flush:"sync"});function f(){r=!0,l.value=!1,o.value=s(n.toValue(e))}return!i&&(n.isRef(e)||typeof e=="function")?n.watch(e,f,{...t,deep:a,immediate:u}):f(),{cloned:o,isModified:l,sync:f}}const ce=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},fe="__vueuse_ssr_handlers__",He=gn();function gn(){return fe in ce||(ce[fe]=ce[fe]||{}),ce[fe]}function de(e,t){return He[e]||t}function bn(e,t){He[e]=t}function Ue(e){return $("(prefers-color-scheme: dark)",e)}function $e(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Re={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Ee="vueuse-storage";function me(e,t,o,l={}){var r;const{flush:i="pre",deep:s=!0,listenToStorageChanges:a=!0,writeDefaults:u=!0,mergeDefaults:f=!1,shallow:c,window:d=L,eventFilter:h,onError:m=D=>{console.error(D)},initOnMounted:v}=l,S=(c?n.shallowRef:n.ref)(typeof t=="function"?t():t),p=n.computed(()=>n.toValue(e));if(!o)try{o=de("getDefaultStorage",()=>{var D;return(D=L)==null?void 0:D.localStorage})()}catch(D){m(D)}if(!o)return S;const y=n.toValue(t),w=$e(y),g=(r=l.serializer)!=null?r:Re[w],{pause:O,resume:E}=b.pausableWatch(S,D=>C(D),{flush:i,deep:s,eventFilter:h});n.watch(p,()=>F(),{flush:i});let P=!1;const T=D=>{v&&!P||F(D)},_=D=>{v&&!P||A(D)};d&&a&&(o instanceof Storage?k(d,"storage",T,{passive:!0}):k(d,Ee,_)),v?b.tryOnMounted(()=>{P=!0,F()}):F();function V(D,N){if(d){const x={key:p.value,oldValue:D,newValue:N,storageArea:o};d.dispatchEvent(o instanceof Storage?new StorageEvent("storage",x):new CustomEvent(Ee,{detail:x}))}}function C(D){try{const N=o.getItem(p.value);if(D==null)V(N,null),o.removeItem(p.value);else{const x=g.write(D);N!==x&&(o.setItem(p.value,x),V(N,x))}}catch(N){m(N)}}function M(D){const N=D?D.newValue:o.getItem(p.value);if(N==null)return u&&y!=null&&o.setItem(p.value,g.write(y)),y;if(!D&&f){const x=g.read(N);return typeof f=="function"?f(x,y):w==="object"&&!Array.isArray(x)?{...y,...x}:x}else return typeof N!="string"?N:g.read(N)}function F(D){if(!(D&&D.storageArea!==o)){if(D&&D.key==null){S.value=y;return}if(!(D&&D.key!==p.value)){O();try{const N=g.write(S.value);(D===void 0||D?.newValue!==N)&&(S.value=M(D))}catch(N){m(N)}finally{D?n.nextTick(E):E()}}}}function A(D){F(D.detail)}return S}const Sn="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function Be(e={}){const{selector:t="html",attribute:o="class",initialValue:l="auto",window:r=L,storage:i,storageKey:s="vueuse-color-scheme",listenToStorageChanges:a=!0,storageRef:u,emitAuto:f,disableTransition:c=!0}=e,d={auto:"",light:"light",dark:"dark",...e.modes||{}},h=Ue({window:r}),m=n.computed(()=>h.value?"dark":"light"),v=u||(s==null?b.toRef(l):me(s,l,i,{window:r,listenToStorageChanges:a})),S=n.computed(()=>v.value==="auto"?m.value:v.value),p=de("updateHTMLAttrs",(O,E,P)=>{const T=typeof O=="string"?r?.document.querySelector(O):W(O);if(!T)return;const _=new Set,V=new Set;let C=null;if(E==="class"){const F=P.split(/\s/g);Object.values(d).flatMap(A=>(A||"").split(/\s/g)).filter(Boolean).forEach(A=>{F.includes(A)?_.add(A):V.add(A)})}else C={key:E,value:P};if(_.size===0&&V.size===0&&C===null)return;let M;c&&(M=r.document.createElement("style"),M.appendChild(document.createTextNode(Sn)),r.document.head.appendChild(M));for(const F of _)T.classList.add(F);for(const F of V)T.classList.remove(F);C&&T.setAttribute(C.key,C.value),c&&(r.getComputedStyle(M).opacity,document.head.removeChild(M))});function y(O){var E;p(t,o,(E=d[O])!=null?E:O)}function w(O){e.onChanged?e.onChanged(O,y):y(O)}n.watch(S,w,{flush:"post",immediate:!0}),b.tryOnMounted(()=>w(S.value));const g=n.computed({get(){return f?v.value:S.value},set(O){v.value=O}});return Object.assign(g,{store:v,system:m,state:S})}function Rn(e=n.shallowRef(!1)){const t=b.createEventHook(),o=b.createEventHook(),l=b.createEventHook();let r=b.noop;const i=u=>(l.trigger(u),e.value=!0,new Promise(f=>{r=f})),s=u=>{e.value=!1,t.trigger(u),r({data:u,isCanceled:!1})},a=u=>{e.value=!1,o.trigger(u),r({data:u,isCanceled:!0})};return{isRevealed:n.computed(()=>e.value),reveal:i,confirm:s,cancel:a,onReveal:l.on,onConfirm:t.on,onCancel:o.on}}function En(e,t){var o,l;const r=n.shallowRef(n.toValue(e)),i=b.useIntervalFn(()=>{var c,d;const h=r.value-1;r.value=h<0?0:h,(c=t?.onTick)==null||c.call(t),r.value<=0&&(i.pause(),(d=t?.onComplete)==null||d.call(t))},(o=t?.interval)!=null?o:1e3,{immediate:(l=t?.immediate)!=null?l:!1}),s=c=>{var d;r.value=(d=n.toValue(c))!=null?d:n.toValue(e)},a=()=>{i.pause(),s()},u=()=>{i.isActive.value||r.value>0&&i.resume()};return{remaining:r,reset:s,stop:a,start:c=>{s(c),i.resume()},pause:i.pause,resume:u,isActive:i.isActive}}function oe(e,t,o={}){const{window:l=L,initialValue:r,observe:i=!1}=o,s=n.shallowRef(r),a=n.computed(()=>{var f;return W(t)||((f=l?.document)==null?void 0:f.documentElement)});function u(){var f;const c=n.toValue(e),d=n.toValue(a);if(d&&l&&c){const h=(f=l.getComputedStyle(d).getPropertyValue(c))==null?void 0:f.trim();s.value=h||s.value||r}}return i&&X(a,u,{attributeFilter:["style","class"],window:l}),n.watch([a,()=>n.toValue(e)],(f,c)=>{c[0]&&c[1]&&c[0].style.removeProperty(c[1]),u()},{immediate:!0}),n.watch([s,a],([f,c])=>{const d=n.toValue(e);c?.style&&d&&(f==null?c.style.removeProperty(d):c.style.setProperty(d,f))},{immediate:!0}),s}function je(e){const t=n.getCurrentInstance(),o=b.computedWithControl(()=>null,()=>e?W(e):t.proxy.$el);return n.onUpdated(o.trigger),n.onMounted(o.trigger),o}function Tn(e,t){const o=n.shallowRef(f()),l=b.toRef(e),r=n.computed({get(){var c;const d=l.value;let h=t?.getIndexOf?t.getIndexOf(o.value,d):d.indexOf(o.value);return h<0&&(h=(c=t?.fallbackIndex)!=null?c:0),h},set(c){i(c)}});function i(c){const d=l.value,h=d.length,m=(c%h+h)%h,v=d[m];return o.value=v,v}function s(c=1){return i(r.value+c)}function a(c=1){return s(c)}function u(c=1){return s(-c)}function f(){var c,d;return(d=n.toValue((c=t?.initialValue)!=null?c:n.toValue(e)[0]))!=null?d:void 0}return n.watch(l,()=>i(r.value)),{state:o,index:r,next:a,prev:u,go:i}}function On(e={}){const{valueDark:t="dark",valueLight:o=""}=e,l=Be({...e,onChanged:(s,a)=>{var u;e.onChanged?(u=e.onChanged)==null||u.call(e,s==="dark",a,s):a(s)},modes:{dark:t,light:o}}),r=n.computed(()=>l.system.value);return n.computed({get(){return l.value==="dark"},set(s){const a=s?"dark":"light";r.value===a?l.value="auto":l.value=a}})}function ze(e){return e}function kn(e,t){return e.value=t}function _n(e){return e?typeof e=="function"?e:ne:ze}function Vn(e){return e?typeof e=="function"?e:ne:ze}function qe(e,t={}){const{clone:o=!1,dump:l=_n(o),parse:r=Vn(o),setSource:i=kn}=t;function s(){return n.markRaw({snapshot:l(e.value),timestamp:b.timestamp()})}const a=n.ref(s()),u=n.ref([]),f=n.ref([]),c=g=>{i(e,r(g.snapshot)),a.value=g},d=()=>{u.value.unshift(a.value),a.value=s(),t.capacity&&u.value.length>t.capacity&&u.value.splice(t.capacity,Number.POSITIVE_INFINITY),f.value.length&&f.value.splice(0,f.value.length)},h=()=>{u.value.splice(0,u.value.length),f.value.splice(0,f.value.length)},m=()=>{const g=u.value.shift();g&&(f.value.unshift(a.value),c(g))},v=()=>{const g=f.value.shift();g&&(u.value.unshift(a.value),c(g))},S=()=>{c(a.value)},p=n.computed(()=>[a.value,...u.value]),y=n.computed(()=>u.value.length>0),w=n.computed(()=>f.value.length>0);return{source:e,undoStack:u,redoStack:f,last:a,history:p,canUndo:y,canRedo:w,clear:h,commit:d,reset:S,undo:m,redo:v}}function Te(e,t={}){const{deep:o=!1,flush:l="pre",eventFilter:r,shouldCommit:i=()=>!0}=t,{eventFilter:s,pause:a,resume:u,isActive:f}=b.pausableFilter(r);let c=e.value;const{ignoreUpdates:d,ignorePrevAsyncUpdates:h,stop:m}=b.watchIgnorable(e,w,{deep:o,flush:l,eventFilter:s});function v(P,T){h(),d(()=>{P.value=T,c=T})}const S=qe(e,{...t,clone:t.clone||o,setSource:v}),{clear:p,commit:y}=S;function w(){h(),i(c,e.value)&&(c=e.value,y())}function g(P){u(),P&&w()}function O(P){let T=!1;const _=()=>T=!0;d(()=>{P(_)}),T||w()}function E(){m(),p()}return{...S,isTracking:f,pause:a,resume:g,commit:w,batch:O,dispose:E}}function Fn(e,t={}){const o=t.debounce?b.debounceFilter(t.debounce):void 0;return{...Te(e,{...t,eventFilter:o})}}function Pn(e={}){const{window:t=L,requestPermissions:o=!1,eventFilter:l=b.bypassFilter}=e,r=H(()=>typeof DeviceMotionEvent<"u"),i=H(()=>r.value&&"requestPermission"in DeviceMotionEvent&&typeof DeviceMotionEvent.requestPermission=="function"),s=n.shallowRef(!1),a=n.ref({x:null,y:null,z:null}),u=n.ref({alpha:null,beta:null,gamma:null}),f=n.shallowRef(0),c=n.ref({x:null,y:null,z:null});function d(){if(t){const m=b.createFilterWrapper(l,v=>{var S,p,y,w,g,O,E,P,T;a.value={x:((S=v.acceleration)==null?void 0:S.x)||null,y:((p=v.acceleration)==null?void 0:p.y)||null,z:((y=v.acceleration)==null?void 0:y.z)||null},c.value={x:((w=v.accelerationIncludingGravity)==null?void 0:w.x)||null,y:((g=v.accelerationIncludingGravity)==null?void 0:g.y)||null,z:((O=v.accelerationIncludingGravity)==null?void 0:O.z)||null},u.value={alpha:((E=v.rotationRate)==null?void 0:E.alpha)||null,beta:((P=v.rotationRate)==null?void 0:P.beta)||null,gamma:((T=v.rotationRate)==null?void 0:T.gamma)||null},f.value=v.interval});k(t,"devicemotion",m,{passive:!0})}}const h=async()=>{if(i.value||(s.value=!0),!s.value&&i.value){const m=DeviceMotionEvent.requestPermission;try{await m()==="granted"&&(s.value=!0,d())}catch(v){console.error(v)}}};return r.value&&(o&&i.value?h().then(()=>d()):d()),{acceleration:a,accelerationIncludingGravity:c,rotationRate:u,interval:f,isSupported:r,requirePermissions:i,ensurePermissions:h,permissionGranted:s}}function Ge(e={}){const{window:t=L}=e,o=H(()=>t&&"DeviceOrientationEvent"in t),l=n.shallowRef(!1),r=n.shallowRef(null),i=n.shallowRef(null),s=n.shallowRef(null);return t&&o.value&&k(t,"deviceorientation",a=>{l.value=a.absolute,r.value=a.alpha,i.value=a.beta,s.value=a.gamma},{passive:!0}),{isSupported:o,isAbsolute:l,alpha:r,beta:i,gamma:s}}function Dn(e={}){const{window:t=L}=e,o=n.shallowRef(1),l=$(()=>`(resolution: ${o.value}dppx)`,e);let r=b.noop;return t&&(r=b.watchImmediate(l,()=>o.value=t.devicePixelRatio)),{pixelRatio:n.readonly(o),stop:r}}function Cn(e={}){const{navigator:t=G,requestPermissions:o=!1,constraints:l={audio:!0,video:!0},onUpdated:r}=e,i=n.ref([]),s=n.computed(()=>i.value.filter(v=>v.kind==="videoinput")),a=n.computed(()=>i.value.filter(v=>v.kind==="audioinput")),u=n.computed(()=>i.value.filter(v=>v.kind==="audiooutput")),f=H(()=>t&&t.mediaDevices&&t.mediaDevices.enumerateDevices),c=n.shallowRef(!1);let d;async function h(){f.value&&(i.value=await t.mediaDevices.enumerateDevices(),r?.(i.value),d&&(d.getTracks().forEach(v=>v.stop()),d=null))}async function m(){const v=l.video?"camera":"microphone";if(!f.value)return!1;if(c.value)return!0;const{state:S,query:p}=ue(v,{controls:!0});if(await p(),S.value!=="granted"){let y=!0;try{const w=await t.mediaDevices.enumerateDevices(),g=w.some(E=>E.kind==="videoinput"),O=w.some(E=>E.kind==="audioinput"||E.kind==="audiooutput");l.video=g?l.video:!1,l.audio=O?l.audio:!1,d=await t.mediaDevices.getUserMedia(l)}catch{d=null,y=!1}h(),c.value=y}else c.value=!0;return c.value}return f.value&&(o&&m(),k(t.mediaDevices,"devicechange",h,{passive:!0}),h()),{devices:i,ensurePermissions:m,permissionGranted:c,videoInputs:s,audioInputs:a,audioOutputs:u,isSupported:f}}function An(e={}){var t;const o=n.shallowRef((t=e.enabled)!=null?t:!1),l=e.video,r=e.audio,{navigator:i=G}=e,s=H(()=>{var m;return(m=i?.mediaDevices)==null?void 0:m.getDisplayMedia}),a={audio:r,video:l},u=n.shallowRef();async function f(){var m;if(!(!s.value||u.value))return u.value=await i.mediaDevices.getDisplayMedia(a),(m=u.value)==null||m.getTracks().forEach(v=>k(v,"ended",d,{passive:!0})),u.value}async function c(){var m;(m=u.value)==null||m.getTracks().forEach(v=>v.stop()),u.value=void 0}function d(){c(),o.value=!1}async function h(){return await f(),u.value&&(o.value=!0),u.value}return n.watch(o,m=>{m?f():c()},{immediate:!0}),{isSupported:s,stream:u,start:h,stop:d,enabled:o}}function Ye(e={}){const{document:t=q}=e;if(!t)return n.shallowRef("visible");const o=n.shallowRef(t.visibilityState);return k(t,"visibilitychange",()=>{o.value=t.visibilityState},{passive:!0}),o}function Mn(e,t={}){var o;const{pointerTypes:l,preventDefault:r,stopPropagation:i,exact:s,onMove:a,onEnd:u,onStart:f,initialValue:c,axis:d="both",draggingElement:h=L,containerElement:m,handle:v=e,buttons:S=[0]}=t,p=n.ref((o=n.toValue(c))!=null?o:{x:0,y:0}),y=n.ref(),w=T=>l?l.includes(T.pointerType):!0,g=T=>{n.toValue(r)&&T.preventDefault(),n.toValue(i)&&T.stopPropagation()},O=T=>{var _;if(!n.toValue(S).includes(T.button)||n.toValue(t.disabled)||!w(T)||n.toValue(s)&&T.target!==n.toValue(e))return;const V=n.toValue(m),C=(_=V?.getBoundingClientRect)==null?void 0:_.call(V),M=n.toValue(e).getBoundingClientRect(),F={x:T.clientX-(V?M.left-C.left+V.scrollLeft:M.left),y:T.clientY-(V?M.top-C.top+V.scrollTop:M.top)};f?.(F,T)!==!1&&(y.value=F,g(T))},E=T=>{if(n.toValue(t.disabled)||!w(T)||!y.value)return;const _=n.toValue(m),V=n.toValue(e).getBoundingClientRect();let{x:C,y:M}=p.value;(d==="x"||d==="both")&&(C=T.clientX-y.value.x,_&&(C=Math.min(Math.max(0,C),_.scrollWidth-V.width))),(d==="y"||d==="both")&&(M=T.clientY-y.value.y,_&&(M=Math.min(Math.max(0,M),_.scrollHeight-V.height))),p.value={x:C,y:M},a?.(p.value,T),g(T)},P=T=>{n.toValue(t.disabled)||!w(T)||y.value&&(y.value=void 0,u?.(p.value,T),g(T))};if(b.isClient){const T=()=>{var _;return{capture:(_=t.capture)!=null?_:!0,passive:!n.toValue(r)}};k(v,"pointerdown",O,T),k(h,"pointermove",E,T),k(h,"pointerup",P,T)}return{...b.toRefs(p),position:p,isDragging:n.computed(()=>!!y.value),style:n.computed(()=>`left:${p.value.x}px;top:${p.value.y}px;`)}}function In(e,t={}){var o,l;const r=n.shallowRef(!1),i=n.shallowRef(null);let s=0,a=!0;if(b.isClient){const u=typeof t=="function"?{onDrop:t}:t,f=(o=u.multiple)!=null?o:!0,c=(l=u.preventDefaultForUnhandled)!=null?l:!1,d=p=>{var y,w;const g=Array.from((w=(y=p.dataTransfer)==null?void 0:y.files)!=null?w:[]);return g.length===0?null:f?g:[g[0]]},h=p=>{const y=n.unref(u.dataTypes);return typeof y=="function"?y(p):y?.length?p.length===0?!1:p.every(w=>y.some(g=>w.includes(g))):!0},m=p=>{const y=Array.from(p??[]).map(O=>O.type),w=h(y),g=f||p.length<=1;return w&&g},v=()=>/^(?:(?!chrome|android).)*safari/i.test(navigator.userAgent)&&!("chrome"in window),S=(p,y)=>{var w,g,O,E,P,T;const _=(w=p.dataTransfer)==null?void 0:w.items;if(a=(g=_&&m(_))!=null?g:!1,c&&p.preventDefault(),!v()&&!a){p.dataTransfer&&(p.dataTransfer.dropEffect="none");return}p.preventDefault(),p.dataTransfer&&(p.dataTransfer.dropEffect="copy");const V=d(p);switch(y){case"enter":s+=1,r.value=!0,(O=u.onEnter)==null||O.call(u,null,p);break;case"over":(E=u.onOver)==null||E.call(u,null,p);break;case"leave":s-=1,s===0&&(r.value=!1),(P=u.onLeave)==null||P.call(u,null,p);break;case"drop":s=0,r.value=!1,a&&(i.value=V,(T=u.onDrop)==null||T.call(u,V,p));break}};k(e,"dragenter",p=>S(p,"enter")),k(e,"dragover",p=>S(p,"over")),k(e,"dragleave",p=>S(p,"leave")),k(e,"drop",p=>S(p,"drop"))}return{files:i,isOverDropZone:r}}function le(e,t,o={}){const{window:l=L,...r}=o;let i;const s=H(()=>l&&"ResizeObserver"in l),a=()=>{i&&(i.disconnect(),i=void 0)},u=n.computed(()=>{const d=n.toValue(e);return Array.isArray(d)?d.map(h=>W(h)):[W(d)]}),f=n.watch(u,d=>{if(a(),s.value&&l){i=new ResizeObserver(t);for(const h of d)h&&i.observe(h,r)}},{immediate:!0,flush:"post"}),c=()=>{a(),f()};return b.tryOnScopeDispose(c),{isSupported:s,stop:c}}function Ln(e,t={}){const{reset:o=!0,windowResize:l=!0,windowScroll:r=!0,immediate:i=!0,updateTiming:s="sync"}=t,a=n.shallowRef(0),u=n.shallowRef(0),f=n.shallowRef(0),c=n.shallowRef(0),d=n.shallowRef(0),h=n.shallowRef(0),m=n.shallowRef(0),v=n.shallowRef(0);function S(){const y=W(e);if(!y){o&&(a.value=0,u.value=0,f.value=0,c.value=0,d.value=0,h.value=0,m.value=0,v.value=0);return}const w=y.getBoundingClientRect();a.value=w.height,u.value=w.bottom,f.value=w.left,c.value=w.right,d.value=w.top,h.value=w.width,m.value=w.x,v.value=w.y}function p(){s==="sync"?S():s==="next-frame"&&requestAnimationFrame(()=>S())}return le(e,p),n.watch(()=>W(e),y=>!y&&p()),X(e,p,{attributeFilter:["style","class"]}),r&&k("scroll",p,{capture:!0,passive:!0}),l&&k("resize",p,{passive:!0}),b.tryOnMounted(()=>{i&&p()}),{height:a,bottom:u,left:f,right:c,top:d,width:h,x:m,y:v,update:p}}function Nn(e){const{x:t,y:o,document:l=q,multiple:r,interval:i="requestAnimationFrame",immediate:s=!0}=e,a=H(()=>n.toValue(r)?l&&"elementsFromPoint"in l:l&&"elementFromPoint"in l),u=n.shallowRef(null),f=()=>{var d,h;u.value=n.toValue(r)?(d=l?.elementsFromPoint(n.toValue(t),n.toValue(o)))!=null?d:[]:(h=l?.elementFromPoint(n.toValue(t),n.toValue(o)))!=null?h:null},c=i==="requestAnimationFrame"?K(f,{immediate:s}):b.useIntervalFn(f,i,{immediate:s});return{isSupported:a,element:u,...c}}function xn(e,t={}){const{delayEnter:o=0,delayLeave:l=0,triggerOnRemoval:r=!1,window:i=L}=t,s=n.shallowRef(!1);let a;const u=f=>{const c=f?o:l;a&&(clearTimeout(a),a=void 0),c?a=setTimeout(()=>s.value=f,c):s.value=f};return i&&(k(e,"mouseenter",()=>u(!0),{passive:!0}),k(e,"mouseleave",()=>u(!1),{passive:!0}),r&&we(n.computed(()=>W(e)),()=>u(!1))),s}function Xe(e,t={width:0,height:0},o={}){const{window:l=L,box:r="content-box"}=o,i=n.computed(()=>{var d,h;return(h=(d=W(e))==null?void 0:d.namespaceURI)==null?void 0:h.includes("svg")}),s=n.shallowRef(t.width),a=n.shallowRef(t.height),{stop:u}=le(e,([d])=>{const h=r==="border-box"?d.borderBoxSize:r==="content-box"?d.contentBoxSize:d.devicePixelContentBoxSize;if(l&&i.value){const m=W(e);if(m){const v=m.getBoundingClientRect();s.value=v.width,a.value=v.height}}else if(h){const m=b.toArray(h);s.value=m.reduce((v,{inlineSize:S})=>v+S,0),a.value=m.reduce((v,{blockSize:S})=>v+S,0)}else s.value=d.contentRect.width,a.value=d.contentRect.height},o);b.tryOnMounted(()=>{const d=W(e);d&&(s.value="offsetWidth"in d?d.offsetWidth:t.width,a.value="offsetHeight"in d?d.offsetHeight:t.height)});const f=n.watch(()=>W(e),d=>{s.value=d?t.width:0,a.value=d?t.height:0});function c(){u(),f()}return{width:s,height:a,stop:c}}function Ke(e,t,o={}){const{root:l,rootMargin:r="0px",threshold:i=0,window:s=L,immediate:a=!0}=o,u=H(()=>s&&"IntersectionObserver"in s),f=n.computed(()=>{const v=n.toValue(e);return b.toArray(v).map(W).filter(b.notNullish)});let c=b.noop;const d=n.shallowRef(a),h=u.value?n.watch(()=>[f.value,W(l),d.value],([v,S])=>{if(c(),!d.value||!v.length)return;const p=new IntersectionObserver(t,{root:W(S),rootMargin:r,threshold:i});v.forEach(y=>y&&p.observe(y)),c=()=>{p.disconnect(),c=b.noop}},{immediate:a,flush:"post"}):b.noop,m=()=>{c(),h(),d.value=!1};return b.tryOnScopeDispose(m),{isSupported:u,isActive:d,pause(){c(),d.value=!1},resume(){d.value=!0},stop:m}}function Je(e,t={}){const{window:o=L,scrollTarget:l,threshold:r=0,rootMargin:i,once:s=!1}=t,a=n.shallowRef(!1),{stop:u}=Ke(e,f=>{let c=a.value,d=0;for(const h of f)h.time>=d&&(d=h.time,c=h.isIntersecting);a.value=c,s&&b.watchOnce(a,()=>{u()})},{root:l,window:o,threshold:r,rootMargin:n.toValue(i)});return a}const ae=new Map;function Wn(e){const t=n.getCurrentScope();function o(a){var u;const f=ae.get(e)||new Set;f.add(a),ae.set(e,f);const c=()=>r(a);return(u=t?.cleanups)==null||u.push(c),c}function l(a){function u(...f){r(u),a(...f)}return o(u)}function r(a){const u=ae.get(e);u&&(u.delete(a),u.size||i())}function i(){ae.delete(e)}function s(a,u){var f;(f=ae.get(e))==null||f.forEach(c=>c(a,u))}return{on:o,once:l,off:r,emit:s,reset:i}}function Hn(e){return e===!0?{}:e}function Un(e,t=[],o={}){const l=n.shallowRef(null),r=n.shallowRef(null),i=n.shallowRef("CONNECTING"),s=n.ref(null),a=n.shallowRef(null),u=b.toRef(e),f=n.shallowRef(null);let c=!1,d=0;const{withCredentials:h=!1,immediate:m=!0,autoConnect:v=!0,autoReconnect:S}=o,p=()=>{b.isClient&&s.value&&(s.value.close(),s.value=null,i.value="CLOSED",c=!0)},y=()=>{if(c||typeof u.value>"u")return;const g=new EventSource(u.value,{withCredentials:h});i.value="CONNECTING",s.value=g,g.onopen=()=>{i.value="OPEN",a.value=null},g.onerror=O=>{if(i.value="CLOSED",a.value=O,g.readyState===2&&!c&&S){g.close();const{retries:E=-1,delay:P=1e3,onFailed:T}=Hn(S);d+=1,typeof E=="number"&&(E<0||d<E)||typeof E=="function"&&E()?setTimeout(y,P):T?.()}},g.onmessage=O=>{l.value=null,r.value=O.data,f.value=O.lastEventId};for(const O of t)k(g,O,E=>{l.value=O,r.value=E.data||null,f.value=E.lastEventId||null},{passive:!0})},w=()=>{b.isClient&&(p(),c=!1,d=0,y())};return m&&w(),v&&n.watch(u,w),b.tryOnScopeDispose(p),{eventSource:s,event:l,data:r,status:i,error:a,open:w,close:p,lastEventId:f}}function $n(e={}){const{initialValue:t=""}=e,o=H(()=>typeof window<"u"&&"EyeDropper"in window),l=n.shallowRef(t);async function r(i){if(!o.value)return;const a=await new window.EyeDropper().open(i);return l.value=a.sRGBHex,a}return{isSupported:o,sRGBHex:l,open:r}}function Bn(e=null,t={}){const{baseUrl:o="",rel:l="icon",document:r=q}=t,i=b.toRef(e),s=a=>{const u=r?.head.querySelectorAll(`link[rel*="${l}"]`);if(!u||u.length===0){const f=r?.createElement("link");f&&(f.rel=l,f.href=`${o}${a}`,f.type=`image/${a.split(".").pop()}`,r?.head.append(f));return}u?.forEach(f=>f.href=`${o}${a}`)};return n.watch(i,(a,u)=>{typeof a=="string"&&a!==u&&s(a)},{immediate:!0}),i}const jn={json:"application/json",text:"text/plain"};function ve(e){return e&&b.containsProp(e,"immediate","refetch","initialData","timeout","beforeFetch","afterFetch","onFetchError","fetch","updateDataOnError")}const zn=/^(?:[a-z][a-z\d+\-.]*:)?\/\//i;function qn(e){return zn.test(e)}function re(e){return typeof Headers<"u"&&e instanceof Headers?Object.fromEntries(e.entries()):e}function Z(e,...t){return e==="overwrite"?async o=>{let l;for(let r=t.length-1;r>=0;r--)if(t[r]!=null){l=t[r];break}return l?{...o,...await l(o)}:o}:async o=>{for(const l of t)l&&(o={...o,...await l(o)});return o}}function Gn(e={}){const t=e.combination||"chain",o=e.options||{},l=e.fetchOptions||{};function r(i,...s){const a=n.computed(()=>{const c=n.toValue(e.baseUrl),d=n.toValue(i);return c&&!qn(d)?Yn(c,d):d});let u=o,f=l;return s.length>0&&(ve(s[0])?u={...u,...s[0],beforeFetch:Z(t,o.beforeFetch,s[0].beforeFetch),afterFetch:Z(t,o.afterFetch,s[0].afterFetch),onFetchError:Z(t,o.onFetchError,s[0].onFetchError)}:f={...f,...s[0],headers:{...re(f.headers)||{},...re(s[0].headers)||{}}}),s.length>1&&ve(s[1])&&(u={...u,...s[1],beforeFetch:Z(t,o.beforeFetch,s[1].beforeFetch),afterFetch:Z(t,o.afterFetch,s[1].afterFetch),onFetchError:Z(t,o.onFetchError,s[1].onFetchError)}),Qe(a,f,u)}return r}function Qe(e,...t){var o,l;const r=typeof AbortController=="function";let i={},s={immediate:!0,refetch:!1,timeout:0,updateDataOnError:!1};const a={method:"GET",type:"text",payload:void 0};t.length>0&&(ve(t[0])?s={...s,...t[0]}:i=t[0]),t.length>1&&ve(t[1])&&(s={...s,...t[1]});const{fetch:u=(l=(o=L)==null?void 0:o.fetch)!=null?l:globalThis?.fetch,initialData:f,timeout:c}=s,d=b.createEventHook(),h=b.createEventHook(),m=b.createEventHook(),v=n.shallowRef(!1),S=n.shallowRef(!1),p=n.shallowRef(!1),y=n.shallowRef(null),w=n.shallowRef(null),g=n.shallowRef(null),O=n.shallowRef(f||null),E=n.computed(()=>r&&S.value);let P,T;const _=I=>{r&&(P?.abort(I),P=new AbortController,P.signal.onabort=()=>p.value=!0,i={...i,signal:P.signal})},V=I=>{S.value=I,v.value=!I};c&&(T=b.useTimeoutFn(_,c,{immediate:!1}));let C=0;const M=async(I=!1)=>{var U,B;_(),V(!0),g.value=null,y.value=null,p.value=!1,C+=1;const j=C,Y={method:a.method,headers:{}},ee=n.toValue(a.payload);if(ee){const z=re(Y.headers),ie=Object.getPrototypeOf(ee);!a.payloadType&&ee&&(ie===Object.prototype||Array.isArray(ie))&&!(ee instanceof FormData)&&(a.payloadType="json"),a.payloadType&&(z["Content-Type"]=(U=jn[a.payloadType])!=null?U:a.payloadType),Y.body=a.payloadType==="json"?JSON.stringify(ee):ee}let _t=!1;const J={url:n.toValue(e),options:{...Y,...i},cancel:()=>{_t=!0}};if(s.beforeFetch&&Object.assign(J,await s.beforeFetch(J)),_t||!u)return V(!1),Promise.resolve(null);let Q=null;return T&&T.start(),u(J.url,{...Y,...J.options,headers:{...re(Y.headers),...re((B=J.options)==null?void 0:B.headers)}}).then(async z=>{if(w.value=z,y.value=z.status,Q=await z.clone()[a.type](),!z.ok)throw O.value=f||null,new Error(z.statusText);return s.afterFetch&&({data:Q}=await s.afterFetch({data:Q,response:z,context:J,execute:M})),O.value=Q,d.trigger(z),z}).catch(async z=>{let ie=z.message||z.name;if(s.onFetchError&&({error:ie,data:Q}=await s.onFetchError({data:Q,error:z,response:w.value,context:J,execute:M})),g.value=ie,s.updateDataOnError&&(O.value=Q),h.trigger(z),I)throw z;return null}).finally(()=>{j===C&&V(!1),T&&T.stop(),m.trigger(null)})},F=b.toRef(s.refetch);n.watch([F,b.toRef(e)],([I])=>I&&M(),{deep:!0});const A={isFinished:n.readonly(v),isFetching:n.readonly(S),statusCode:y,response:w,error:g,data:O,canAbort:E,aborted:p,abort:_,execute:M,onFetchResponse:d.on,onFetchError:h.on,onFetchFinally:m.on,get:D("GET"),put:D("PUT"),post:D("POST"),delete:D("DELETE"),patch:D("PATCH"),head:D("HEAD"),options:D("OPTIONS"),json:x("json"),text:x("text"),blob:x("blob"),arrayBuffer:x("arrayBuffer"),formData:x("formData")};function D(I){return(U,B)=>{if(!S.value)return a.method=I,a.payload=U,a.payloadType=B,n.isRef(a.payload)&&n.watch([F,b.toRef(a.payload)],([j])=>j&&M(),{deep:!0}),{...A,then(j,Y){return N().then(j,Y)}}}}function N(){return new Promise((I,U)=>{b.until(v).toBe(!0).then(()=>I(A)).catch(U)})}function x(I){return()=>{if(!S.value)return a.type=I,{...A,then(U,B){return N().then(U,B)}}}}return s.immediate&&Promise.resolve().then(()=>M()),{...A,then(I,U){return N().then(I,U)}}}function Yn(e,t){return!e.endsWith("/")&&!t.startsWith("/")?`${e}/${t}`:e.endsWith("/")&&t.startsWith("/")?`${e.slice(0,-1)}${t}`:`${e}${t}`}const Xn={multiple:!0,accept:"*",reset:!1,directory:!1};function Kn(e){if(!e)return null;if(e instanceof FileList)return e;const t=new DataTransfer;for(const o of e)t.items.add(o);return t.files}function Jn(e={}){const{document:t=q}=e,o=n.ref(Kn(e.initialFiles)),{on:l,trigger:r}=b.createEventHook(),{on:i,trigger:s}=b.createEventHook(),a=n.computed(()=>{var d;const h=(d=W(e.input))!=null?d:t?t.createElement("input"):void 0;return h&&(h.type="file",h.onchange=m=>{const v=m.target;o.value=v.files,r(o.value)},h.oncancel=()=>{s()}),h}),u=()=>{o.value=null,a.value&&a.value.value&&(a.value.value="",r(null))},f=d=>{const h=a.value;h&&(h.multiple=n.toValue(d.multiple),h.accept=n.toValue(d.accept),h.webkitdirectory=n.toValue(d.directory),b.hasOwn(d,"capture")&&(h.capture=n.toValue(d.capture)))},c=d=>{const h=a.value;if(!h)return;const m={...Xn,...e,...d};f(m),n.toValue(m.reset)&&u(),h.click()};return n.watchEffect(()=>{f(e)}),{files:n.readonly(o),open:c,reset:u,onCancel:i,onChange:l}}function Qn(e={}){const{window:t=L,dataType:o="Text"}=e,l=t,r=H(()=>l&&"showSaveFilePicker"in l&&"showOpenFilePicker"in l),i=n.shallowRef(),s=n.shallowRef(),a=n.shallowRef(),u=n.computed(()=>{var w,g;return(g=(w=a.value)==null?void 0:w.name)!=null?g:""}),f=n.computed(()=>{var w,g;return(g=(w=a.value)==null?void 0:w.type)!=null?g:""}),c=n.computed(()=>{var w,g;return(g=(w=a.value)==null?void 0:w.size)!=null?g:0}),d=n.computed(()=>{var w,g;return(g=(w=a.value)==null?void 0:w.lastModified)!=null?g:0});async function h(w={}){if(!r.value)return;const[g]=await l.showOpenFilePicker({...n.toValue(e),...w});i.value=g,await y()}async function m(w={}){r.value&&(i.value=await l.showSaveFilePicker({...e,...w}),s.value=void 0,await y())}async function v(w={}){if(r.value){if(!i.value)return S(w);if(s.value){const g=await i.value.createWritable();await g.write(s.value),await g.close()}await p()}}async function S(w={}){if(r.value){if(i.value=await l.showSaveFilePicker({...e,...w}),s.value){const g=await i.value.createWritable();await g.write(s.value),await g.close()}await p()}}async function p(){var w;a.value=await((w=i.value)==null?void 0:w.getFile())}async function y(){var w,g;await p();const O=n.toValue(o);O==="Text"?s.value=await((w=a.value)==null?void 0:w.text()):O==="ArrayBuffer"?s.value=await((g=a.value)==null?void 0:g.arrayBuffer()):O==="Blob"&&(s.value=a.value)}return n.watch(()=>n.toValue(o),y),{isSupported:r,data:s,file:a,fileName:u,fileMIME:f,fileSize:c,fileLastModified:d,open:h,create:m,save:v,saveAs:S,updateData:y}}function Zn(e,t={}){const{initialValue:o=!1,focusVisible:l=!1,preventScroll:r=!1}=t,i=n.shallowRef(!1),s=n.computed(()=>W(e)),a={passive:!0};k(s,"focus",f=>{var c,d;(!l||(d=(c=f.target).matches)!=null&&d.call(c,":focus-visible"))&&(i.value=!0)},a),k(s,"blur",()=>i.value=!1,a);const u=n.computed({get:()=>i.value,set(f){var c,d;!f&&i.value?(c=s.value)==null||c.blur():f&&!i.value&&((d=s.value)==null||d.focus({preventScroll:r}))}});return n.watch(s,()=>{u.value=o},{immediate:!0,flush:"post"}),{focused:u}}const eo="focusin",to="focusout",no=":focus-within";function oo(e,t={}){const{window:o=L}=t,l=n.computed(()=>W(e)),r=n.shallowRef(!1),i=n.computed(()=>r.value);if(!o||!Le(t).value)return{focused:i};const a={passive:!0};return k(l,eo,()=>r.value=!0,a),k(l,to,()=>{var u,f,c;return r.value=(c=(f=(u=l.value)==null?void 0:u.matches)==null?void 0:f.call(u,no))!=null?c:!1},a),{focused:i}}function lo(e){var t;const o=n.shallowRef(0);if(typeof performance>"u")return o;const l=(t=e?.every)!=null?t:10;let r=performance.now(),i=0;return K(()=>{if(i+=1,i>=l){const s=performance.now(),a=s-r;o.value=Math.round(1e3/(a/i)),r=s,i=0}}),o}const Ze=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function ao(e,t={}){const{document:o=q,autoExit:l=!1}=t,r=n.computed(()=>{var w;return(w=W(e))!=null?w:o?.documentElement}),i=n.shallowRef(!1),s=n.computed(()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find(w=>o&&w in o||r.value&&w in r.value)),a=n.computed(()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find(w=>o&&w in o||r.value&&w in r.value)),u=n.computed(()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find(w=>o&&w in o||r.value&&w in r.value)),f=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find(w=>o&&w in o),c=H(()=>r.value&&o&&s.value!==void 0&&a.value!==void 0&&u.value!==void 0),d=()=>f?o?.[f]===r.value:!1,h=()=>{if(u.value){if(o&&o[u.value]!=null)return o[u.value];{const w=r.value;if(w?.[u.value]!=null)return!!w[u.value]}}return!1};async function m(){if(!(!c.value||!i.value)){if(a.value)if(o?.[a.value]!=null)await o[a.value]();else{const w=r.value;w?.[a.value]!=null&&await w[a.value]()}i.value=!1}}async function v(){if(!c.value||i.value)return;h()&&await m();const w=r.value;s.value&&w?.[s.value]!=null&&(await w[s.value](),i.value=!0)}async function S(){await(i.value?m():v())}const p=()=>{const w=h();(!w||w&&d())&&(i.value=w)},y={capture:!1,passive:!0};return k(o,Ze,p,y),k(()=>W(r),Ze,p,y),b.tryOnMounted(p,!1),l&&b.tryOnScopeDispose(m),{isSupported:c,isFullscreen:i,enter:v,exit:m,toggle:S}}function ro(e){return n.computed(()=>e.value?{buttons:{a:e.value.buttons[0],b:e.value.buttons[1],x:e.value.buttons[2],y:e.value.buttons[3]},bumper:{left:e.value.buttons[4],right:e.value.buttons[5]},triggers:{left:e.value.buttons[6],right:e.value.buttons[7]},stick:{left:{horizontal:e.value.axes[0],vertical:e.value.axes[1],button:e.value.buttons[10]},right:{horizontal:e.value.axes[2],vertical:e.value.axes[3],button:e.value.buttons[11]}},dpad:{up:e.value.buttons[12],down:e.value.buttons[13],left:e.value.buttons[14],right:e.value.buttons[15]},back:e.value.buttons[8],start:e.value.buttons[9]}:null)}function io(e={}){const{navigator:t=G}=e,o=H(()=>t&&"getGamepads"in t),l=n.ref([]),r=b.createEventHook(),i=b.createEventHook(),s=v=>{const S=[],p="vibrationActuator"in v?v.vibrationActuator:null;return p&&S.push(p),v.hapticActuators&&S.push(...v.hapticActuators),{id:v.id,index:v.index,connected:v.connected,mapping:v.mapping,timestamp:v.timestamp,vibrationActuator:v.vibrationActuator,hapticActuators:S,axes:v.axes.map(y=>y),buttons:v.buttons.map(y=>({pressed:y.pressed,touched:y.touched,value:y.value}))}},a=()=>{const v=t?.getGamepads()||[];for(const S of v)S&&l.value[S.index]&&(l.value[S.index]=s(S))},{isActive:u,pause:f,resume:c}=K(a),d=v=>{l.value.some(({index:S})=>S===v.index)||(l.value.push(s(v)),r.trigger(v.index)),c()},h=v=>{l.value=l.value.filter(S=>S.index!==v.index),i.trigger(v.index)},m={passive:!0};return k("gamepadconnected",v=>d(v.gamepad),m),k("gamepaddisconnected",v=>h(v.gamepad),m),b.tryOnMounted(()=>{const v=t?.getGamepads()||[];for(const S of v)S&&l.value[S.index]&&d(S)}),f(),{isSupported:o,onConnected:r.on,onDisconnected:i.on,gamepads:l,pause:f,resume:c,isActive:u}}function so(e={}){const{enableHighAccuracy:t=!0,maximumAge:o=3e4,timeout:l=27e3,navigator:r=G,immediate:i=!0}=e,s=H(()=>r&&"geolocation"in r),a=n.shallowRef(null),u=n.shallowRef(null),f=n.ref({accuracy:0,latitude:Number.POSITIVE_INFINITY,longitude:Number.POSITIVE_INFINITY,altitude:null,altitudeAccuracy:null,heading:null,speed:null});function c(v){a.value=v.timestamp,f.value=v.coords,u.value=null}let d;function h(){s.value&&(d=r.geolocation.watchPosition(c,v=>u.value=v,{enableHighAccuracy:t,maximumAge:o,timeout:l}))}i&&h();function m(){d&&r&&r.geolocation.clearWatch(d)}return b.tryOnScopeDispose(()=>{m()}),{isSupported:s,coords:f,locatedAt:a,error:u,resume:h,pause:m}}const uo=["mousemove","mousedown","resize","keydown","touchstart","wheel"],co=6e4;function fo(e=co,t={}){const{initialState:o=!1,listenForVisibilityChange:l=!0,events:r=uo,window:i=L,eventFilter:s=b.throttleFilter(50)}=t,a=n.shallowRef(o),u=n.shallowRef(b.timestamp());let f;const c=()=>{a.value=!1,clearTimeout(f),f=setTimeout(()=>a.value=!0,e)},d=b.createFilterWrapper(s,()=>{u.value=b.timestamp(),c()});if(i){const h=i.document,m={passive:!0};for(const v of r)k(i,v,d,m);l&&k(h,"visibilitychange",()=>{h.hidden||d()},m),o||c()}return{idle:a,lastActive:u,reset:c}}async function mo(e){return new Promise((t,o)=>{const l=new Image,{src:r,srcset:i,sizes:s,class:a,loading:u,crossorigin:f,referrerPolicy:c,width:d,height:h,decoding:m,fetchPriority:v,ismap:S,usemap:p}=e;l.src=r,i!=null&&(l.srcset=i),s!=null&&(l.sizes=s),a!=null&&(l.className=a),u!=null&&(l.loading=u),f!=null&&(l.crossOrigin=f),c!=null&&(l.referrerPolicy=c),d!=null&&(l.width=d),h!=null&&(l.height=h),m!=null&&(l.decoding=m),v!=null&&(l.fetchPriority=v),S!=null&&(l.isMap=S),p!=null&&(l.useMap=p),l.onload=()=>t(l),l.onerror=o})}function vo(e,t={}){const o=Ne(()=>mo(n.toValue(e)),void 0,{resetOnExecute:!0,...t});return n.watch(()=>n.toValue(e),()=>o.execute(t.delay),{deep:!0}),o}function pe(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}const et=1;function Oe(e,t={}){const{throttle:o=0,idle:l=200,onStop:r=b.noop,onScroll:i=b.noop,offset:s={left:0,right:0,top:0,bottom:0},observe:a={mutation:!1},eventListenerOptions:u={capture:!1,passive:!0},behavior:f="auto",window:c=L,onError:d=V=>{console.error(V)}}=t,h=typeof a=="boolean"?{mutation:a}:a,m=n.shallowRef(0),v=n.shallowRef(0),S=n.computed({get(){return m.value},set(V){y(V,void 0)}}),p=n.computed({get(){return v.value},set(V){y(void 0,V)}});function y(V,C){var M,F,A,D;if(!c)return;const N=n.toValue(e);if(!N)return;(A=N instanceof Document?c.document.body:N)==null||A.scrollTo({top:(M=n.toValue(C))!=null?M:p.value,left:(F=n.toValue(V))!=null?F:S.value,behavior:n.toValue(f)});const x=((D=N?.document)==null?void 0:D.documentElement)||N?.documentElement||N;S!=null&&(m.value=x.scrollLeft),p!=null&&(v.value=x.scrollTop)}const w=n.shallowRef(!1),g=n.reactive({left:!0,right:!1,top:!0,bottom:!1}),O=n.reactive({left:!1,right:!1,top:!1,bottom:!1}),E=V=>{w.value&&(w.value=!1,O.left=!1,O.right=!1,O.top=!1,O.bottom=!1,r(V))},P=b.useDebounceFn(E,o+l),T=V=>{var C;if(!c)return;const M=((C=V?.document)==null?void 0:C.documentElement)||V?.documentElement||W(V),{display:F,flexDirection:A,direction:D}=getComputedStyle(M),N=D==="rtl"?-1:1,x=M.scrollLeft;O.left=x<m.value,O.right=x>m.value;const I=Math.abs(x*N)<=(s.left||0),U=Math.abs(x*N)+M.clientWidth>=M.scrollWidth-(s.right||0)-et;F==="flex"&&A==="row-reverse"?(g.left=U,g.right=I):(g.left=I,g.right=U),m.value=x;let B=M.scrollTop;V===c.document&&!B&&(B=c.document.body.scrollTop),O.top=B<v.value,O.bottom=B>v.value;const j=Math.abs(B)<=(s.top||0),Y=Math.abs(B)+M.clientHeight>=M.scrollHeight-(s.bottom||0)-et;F==="flex"&&A==="column-reverse"?(g.top=Y,g.bottom=j):(g.top=j,g.bottom=Y),v.value=B},_=V=>{var C;if(!c)return;const M=(C=V.target.documentElement)!=null?C:V.target;T(M),w.value=!0,P(V),i(V)};return k(e,"scroll",o?b.useThrottleFn(_,o,!0,!1):_,u),b.tryOnMounted(()=>{try{const V=n.toValue(e);if(!V)return;T(V)}catch(V){d(V)}}),h?.mutation&&e!=null&&e!==c&&e!==document&&X(e,()=>{const V=n.toValue(e);V&&T(V)},{attributes:!0,childList:!0,subtree:!0}),k(e,"scrollend",E,u),{x:S,y:p,isScrolling:w,arrivedState:g,directions:O,measure(){const V=n.toValue(e);c&&V&&T(V)}}}function po(e,t,o={}){var l;const{direction:r="bottom",interval:i=100,canLoadMore:s=()=>!0}=o,a=n.reactive(Oe(e,{...o,offset:{[r]:(l=o.distance)!=null?l:0,...o.offset}})),u=n.ref(),f=n.computed(()=>!!u.value),c=n.computed(()=>pe(n.toValue(e))),d=Je(c);function h(){if(a.measure(),!c.value||!d.value||!s(c.value))return;const{scrollHeight:v,clientHeight:S,scrollWidth:p,clientWidth:y}=c.value,w=r==="bottom"||r==="top"?v<=S:p<=y;(a.arrivedState[r]||w)&&(u.value||(u.value=Promise.all([t(a),new Promise(g=>setTimeout(g,i))]).finally(()=>{u.value=null,n.nextTick(()=>h())})))}const m=n.watch(()=>[a.arrivedState[r],d.value],h,{immediate:!0});return b.tryOnUnmounted(m),{isLoading:f,reset(){n.nextTick(()=>h())}}}const ho=["mousedown","mouseup","keydown","keyup"];function yo(e,t={}){const{events:o=ho,document:l=q,initial:r=null}=t,i=n.shallowRef(r);return l&&o.forEach(s=>{k(l,s,a=>{typeof a.getModifierState=="function"&&(i.value=a.getModifierState(e))},{passive:!0})}),i}function wo(e,t,o={}){const{window:l=L}=o;return me(e,t,l?.localStorage,o)}const tt={ctrl:"control",command:"meta",cmd:"meta",option:"alt",up:"arrowup",down:"arrowdown",left:"arrowleft",right:"arrowright"};function go(e={}){const{reactive:t=!1,target:o=L,aliasMap:l=tt,passive:r=!0,onEventFired:i=b.noop}=e,s=n.reactive(new Set),a={toJSON(){return{}},current:s},u=t?n.reactive(a):a,f=new Set,c=new Set,d=new Set;function h(p,y){p in u&&(t?u[p]=y:u[p].value=y)}function m(){s.clear();for(const p of d)h(p,!1)}function v(p,y){var w,g;const O=(w=p.key)==null?void 0:w.toLowerCase(),P=[(g=p.code)==null?void 0:g.toLowerCase(),O].filter(Boolean);O&&(y?s.add(O):s.delete(O));for(const T of P)d.add(T),h(T,y);if(O==="shift"&&!y){const T=Array.from(c),_=T.indexOf("shift");T.forEach((V,C)=>{C>=_&&(s.delete(V),h(V,!1))}),c.clear()}else typeof p.getModifierState=="function"&&p.getModifierState("Shift")&&y&&[...s,...P].forEach(T=>c.add(T));O==="meta"&&!y?(f.forEach(T=>{s.delete(T),h(T,!1)}),f.clear()):typeof p.getModifierState=="function"&&p.getModifierState("Meta")&&y&&[...s,...P].forEach(T=>f.add(T))}k(o,"keydown",p=>(v(p,!0),i(p)),{passive:r}),k(o,"keyup",p=>(v(p,!1),i(p)),{passive:r}),k("blur",m,{passive:r}),k("focus",m,{passive:r});const S=new Proxy(u,{get(p,y,w){if(typeof y!="string")return Reflect.get(p,y,w);if(y=y.toLowerCase(),y in l&&(y=l[y]),!(y in u))if(/[+_-]/.test(y)){const O=y.split(/[+_-]/g).map(E=>E.trim());u[y]=n.computed(()=>O.map(E=>n.toValue(S[E])).every(Boolean))}else u[y]=n.shallowRef(!1);const g=Reflect.get(p,y,w);return t?n.toValue(g):g}});return S}function ke(e,t){n.toValue(e)&&t(n.toValue(e))}function bo(e){let t=[];for(let o=0;o<e.length;++o)t=[...t,[e.start(o),e.end(o)]];return t}function _e(e){return Array.from(e).map(({label:t,kind:o,language:l,mode:r,activeCues:i,cues:s,inBandMetadataTrackDispatchType:a},u)=>({id:u,label:t,kind:o,language:l,mode:r,activeCues:i,cues:s,inBandMetadataTrackDispatchType:a}))}const So={src:"",tracks:[]};function Ro(e,t={}){e=b.toRef(e),t={...So,...t};const{document:o=q}=t,l={passive:!0},r=n.shallowRef(0),i=n.shallowRef(0),s=n.shallowRef(!1),a=n.shallowRef(1),u=n.shallowRef(!1),f=n.shallowRef(!1),c=n.shallowRef(!1),d=n.shallowRef(1),h=n.shallowRef(!1),m=n.ref([]),v=n.ref([]),S=n.shallowRef(-1),p=n.shallowRef(!1),y=n.shallowRef(!1),w=o&&"pictureInPictureEnabled"in o,g=b.createEventHook(),O=b.createEventHook(),E=F=>{ke(e,A=>{if(F){const D=typeof F=="number"?F:F.id;A.textTracks[D].mode="disabled"}else for(let D=0;D<A.textTracks.length;++D)A.textTracks[D].mode="disabled";S.value=-1})},P=(F,A=!0)=>{ke(e,D=>{const N=typeof F=="number"?F:F.id;A&&E(),D.textTracks[N].mode="showing",S.value=N})},T=()=>new Promise((F,A)=>{ke(e,async D=>{w&&(p.value?o.exitPictureInPicture().then(F).catch(A):D.requestPictureInPicture().then(F).catch(A))})});n.watchEffect(()=>{if(!o)return;const F=n.toValue(e);if(!F)return;const A=n.toValue(t.src);let D=[];A&&(typeof A=="string"?D=[{src:A}]:Array.isArray(A)?D=A:b.isObject(A)&&(D=[A]),F.querySelectorAll("source").forEach(N=>{N.remove()}),D.forEach(({src:N,type:x,media:I})=>{const U=o.createElement("source");U.setAttribute("src",N),U.setAttribute("type",x||""),U.setAttribute("media",I||""),k(U,"error",g.trigger,l),F.appendChild(U)}),F.load())}),n.watch([e,a],()=>{const F=n.toValue(e);F&&(F.volume=a.value)}),n.watch([e,y],()=>{const F=n.toValue(e);F&&(F.muted=y.value)}),n.watch([e,d],()=>{const F=n.toValue(e);F&&(F.playbackRate=d.value)}),n.watchEffect(()=>{if(!o)return;const F=n.toValue(t.tracks),A=n.toValue(e);!F||!F.length||!A||(A.querySelectorAll("track").forEach(D=>D.remove()),F.forEach(({default:D,kind:N,label:x,src:I,srcLang:U},B)=>{const j=o.createElement("track");j.default=D||!1,j.kind=N,j.label=x,j.src=I,j.srclang=U,j.default&&(S.value=B),A.appendChild(j)}))});const{ignoreUpdates:_}=b.watchIgnorable(r,F=>{const A=n.toValue(e);A&&(A.currentTime=F)}),{ignoreUpdates:V}=b.watchIgnorable(c,F=>{const A=n.toValue(e);A&&(F?A.play().catch(D=>{throw O.trigger(D),D}):A.pause())});k(e,"timeupdate",()=>_(()=>r.value=n.toValue(e).currentTime),l),k(e,"durationchange",()=>i.value=n.toValue(e).duration,l),k(e,"progress",()=>m.value=bo(n.toValue(e).buffered),l),k(e,"seeking",()=>s.value=!0,l),k(e,"seeked",()=>s.value=!1,l),k(e,["waiting","loadstart"],()=>{u.value=!0,V(()=>c.value=!1)},l),k(e,"loadeddata",()=>u.value=!1,l),k(e,"playing",()=>{u.value=!1,f.value=!1,V(()=>c.value=!0)},l),k(e,"ratechange",()=>d.value=n.toValue(e).playbackRate,l),k(e,"stalled",()=>h.value=!0,l),k(e,"ended",()=>f.value=!0,l),k(e,"pause",()=>V(()=>c.value=!1),l),k(e,"play",()=>V(()=>c.value=!0),l),k(e,"enterpictureinpicture",()=>p.value=!0,l),k(e,"leavepictureinpicture",()=>p.value=!1,l),k(e,"volumechange",()=>{const F=n.toValue(e);F&&(a.value=F.volume,y.value=F.muted)},l);const C=[],M=n.watch([e],()=>{const F=n.toValue(e);F&&(M(),C[0]=k(F.textTracks,"addtrack",()=>v.value=_e(F.textTracks),l),C[1]=k(F.textTracks,"removetrack",()=>v.value=_e(F.textTracks),l),C[2]=k(F.textTracks,"change",()=>v.value=_e(F.textTracks),l))});return b.tryOnScopeDispose(()=>C.forEach(F=>F())),{currentTime:r,duration:i,waiting:u,seeking:s,ended:f,stalled:h,buffered:m,playing:c,rate:d,volume:a,muted:y,tracks:v,selectedTrack:S,enableTrack:P,disableTrack:E,supportsPictureInPicture:w,togglePictureInPicture:T,isPictureInPicture:p,onSourceError:g.on,onPlaybackError:O.on}}function Eo(e,t){const l=t?.cache?n.shallowReactive(t.cache):n.shallowReactive(new Map),r=(...c)=>t?.getKey?t.getKey(...c):JSON.stringify(c),i=(c,...d)=>(l.set(c,e(...d)),l.get(c)),s=(...c)=>i(r(...c),...c),a=(...c)=>{l.delete(r(...c))},u=()=>{l.clear()},f=(...c)=>{const d=r(...c);return l.has(d)?l.get(d):i(d,...c)};return f.load=s,f.delete=a,f.clear=u,f.generateKey=r,f.cache=l,f}function To(e={}){const t=n.ref(),o=H(()=>typeof performance<"u"&&"memory"in performance);if(o.value){const{interval:l=1e3}=e;b.useIntervalFn(()=>{t.value=performance.memory},l,{immediate:e.immediate,immediateCallback:e.immediateCallback})}return{isSupported:o,memory:t}}const Oo={page:e=>[e.pageX,e.pageY],client:e=>[e.clientX,e.clientY],screen:e=>[e.screenX,e.screenY],movement:e=>e instanceof MouseEvent?[e.movementX,e.movementY]:null};function nt(e={}){const{type:t="page",touch:o=!0,resetOnTouchEnds:l=!1,initialValue:r={x:0,y:0},window:i=L,target:s=i,scroll:a=!0,eventFilter:u}=e;let f=null,c=0,d=0;const h=n.shallowRef(r.x),m=n.shallowRef(r.y),v=n.shallowRef(null),S=typeof t=="function"?t:Oo[t],p=T=>{const _=S(T);f=T,_&&([h.value,m.value]=_,v.value="mouse"),i&&(c=i.scrollX,d=i.scrollY)},y=T=>{if(T.touches.length>0){const _=S(T.touches[0]);_&&([h.value,m.value]=_,v.value="touch")}},w=()=>{if(!f||!i)return;const T=S(f);f instanceof MouseEvent&&T&&(h.value=T[0]+i.scrollX-c,m.value=T[1]+i.scrollY-d)},g=()=>{h.value=r.x,m.value=r.y},O=u?T=>u(()=>p(T),{}):T=>p(T),E=u?T=>u(()=>y(T),{}):T=>y(T),P=u?()=>u(()=>w(),{}):()=>w();if(s){const T={passive:!0};k(s,["mousemove","dragover"],O,T),o&&t!=="movement"&&(k(s,["touchstart","touchmove"],E,T),l&&k(s,"touchend",g,T)),a&&t==="page"&&k(i,"scroll",P,T)}return{x:h,y:m,sourceType:v}}function ot(e,t={}){const{windowResize:o=!0,windowScroll:l=!0,handleOutside:r=!0,window:i=L}=t,s=t.type||"page",{x:a,y:u,sourceType:f}=nt(t),c=n.shallowRef(e??i?.document.body),d=n.shallowRef(0),h=n.shallowRef(0),m=n.shallowRef(0),v=n.shallowRef(0),S=n.shallowRef(0),p=n.shallowRef(0),y=n.shallowRef(!0);function w(){if(!i)return;const E=W(c);if(!E||!(E instanceof Element))return;const{left:P,top:T,width:_,height:V}=E.getBoundingClientRect();m.value=P+(s==="page"?i.pageXOffset:0),v.value=T+(s==="page"?i.pageYOffset:0),S.value=V,p.value=_;const C=a.value-m.value,M=u.value-v.value;y.value=_===0||V===0||C<0||M<0||C>_||M>V,(r||!y.value)&&(d.value=C,h.value=M)}const g=[];function O(){g.forEach(E=>E()),g.length=0}if(b.tryOnMounted(()=>{w()}),i){const{stop:E}=le(c,w),{stop:P}=X(c,w,{attributeFilter:["style","class"]}),T=n.watch([c,a,u],w);g.push(E,P,T),k(document,"mouseleave",()=>y.value=!0,{passive:!0}),l&&g.push(k("scroll",w,{capture:!0,passive:!0})),o&&g.push(k("resize",w,{passive:!0}))}return{x:a,y:u,sourceType:f,elementX:d,elementY:h,elementPositionX:m,elementPositionY:v,elementHeight:S,elementWidth:p,isOutside:y,stop:O}}function ko(e={}){const{touch:t=!0,drag:o=!0,capture:l=!1,initialValue:r=!1,window:i=L}=e,s=n.shallowRef(r),a=n.shallowRef(null);if(!i)return{pressed:s,sourceType:a};const u=h=>m=>{var v;s.value=!0,a.value=h,(v=e.onPressed)==null||v.call(e,m)},f=h=>{var m;s.value=!1,a.value=null,(m=e.onReleased)==null||m.call(e,h)},c=n.computed(()=>W(e.target)||i),d={passive:!0,capture:l};return k(c,"mousedown",u("mouse"),d),k(i,"mouseleave",f,d),k(i,"mouseup",f,d),o&&(k(c,"dragstart",u("mouse"),d),k(i,"drop",f,d),k(i,"dragend",f,d)),t&&(k(c,"touchstart",u("touch"),d),k(i,"touchend",f,d),k(i,"touchcancel",f,d)),{pressed:s,sourceType:a}}function _o(e={}){const{window:t=L}=e,o=t?.navigator,l=H(()=>o&&"language"in o),r=n.shallowRef(o?.language);return k(t,"languagechange",()=>{o&&(r.value=o.language)},{passive:!0}),{isSupported:l,language:r}}function lt(e={}){const{window:t=L}=e,o=t?.navigator,l=H(()=>o&&"connection"in o),r=n.shallowRef(!0),i=n.shallowRef(!1),s=n.shallowRef(void 0),a=n.shallowRef(void 0),u=n.shallowRef(void 0),f=n.shallowRef(void 0),c=n.shallowRef(void 0),d=n.shallowRef(void 0),h=n.shallowRef("unknown"),m=l.value&&o.connection;function v(){o&&(r.value=o.onLine,s.value=r.value?void 0:Date.now(),a.value=r.value?Date.now():void 0,m&&(u.value=m.downlink,f.value=m.downlinkMax,d.value=m.effectiveType,c.value=m.rtt,i.value=m.saveData,h.value=m.type))}const S={passive:!0};return t&&(k(t,"offline",()=>{r.value=!1,s.value=Date.now()},S),k(t,"online",()=>{r.value=!0,a.value=Date.now()},S)),m&&k(m,"change",v,S),v(),{isSupported:l,isOnline:n.readonly(r),saveData:n.readonly(i),offlineAt:n.readonly(s),onlineAt:n.readonly(a),downlink:n.readonly(u),downlinkMax:n.readonly(f),effectiveType:n.readonly(d),rtt:n.readonly(c),type:n.readonly(h)}}function Ve(e={}){const{controls:t=!1,interval:o="requestAnimationFrame",immediate:l=!0}=e,r=n.ref(new Date),i=()=>r.value=new Date,s=o==="requestAnimationFrame"?K(i,{immediate:l}):b.useIntervalFn(i,o,{immediate:l});return t?{now:r,...s}:r}function Vo(e){const t=n.shallowRef(),o=()=>{t.value&&URL.revokeObjectURL(t.value),t.value=void 0};return n.watch(()=>n.toValue(e),l=>{o(),l&&(t.value=URL.createObjectURL(l))},{immediate:!0}),b.tryOnScopeDispose(o),n.readonly(t)}function at(e,t,o){if(typeof e=="function"||n.isReadonly(e))return n.computed(()=>b.clamp(n.toValue(e),n.toValue(t),n.toValue(o)));const l=n.ref(e);return n.computed({get(){return l.value=b.clamp(l.value,n.toValue(t),n.toValue(o))},set(r){l.value=b.clamp(r,n.toValue(t),n.toValue(o))}})}function Fo(e){const{total:t=Number.POSITIVE_INFINITY,pageSize:o=10,page:l=1,onPageChange:r=b.noop,onPageSizeChange:i=b.noop,onPageCountChange:s=b.noop}=e,a=at(o,1,Number.POSITIVE_INFINITY),u=n.computed(()=>Math.max(1,Math.ceil(n.toValue(t)/n.toValue(a)))),f=at(l,1,u),c=n.computed(()=>f.value===1),d=n.computed(()=>f.value===u.value);n.isRef(l)&&b.syncRef(l,f,{direction:n.isReadonly(l)?"ltr":"both"}),n.isRef(o)&&b.syncRef(o,a,{direction:n.isReadonly(o)?"ltr":"both"});function h(){f.value--}function m(){f.value++}const v={currentPage:f,currentPageSize:a,pageCount:u,isFirstPage:c,isLastPage:d,prev:h,next:m};return n.watch(f,()=>{r(n.reactive(v))}),n.watch(a,()=>{i(n.reactive(v))}),n.watch(u,()=>{s(n.reactive(v))}),v}function Po(e={}){const{isOnline:t}=lt(e);return t}function Do(e={}){const{window:t=L}=e,o=n.shallowRef(!1),l=r=>{if(!t)return;r=r||t.event;const i=r.relatedTarget||r.toElement;o.value=!i};if(t){const r={passive:!0};k(t,"mouseout",l,r),k(t.document,"mouseleave",l,r),k(t.document,"mouseenter",l,r)}return o}function rt(e={}){const{window:t=L}=e,o=H(()=>t&&"screen"in t&&"orientation"in t.screen),l=o.value?t.screen.orientation:{},r=n.ref(l.type),i=n.shallowRef(l.angle||0);return o.value&&k(t,"orientationchange",()=>{r.value=l.type,i.value=l.angle},{passive:!0}),{isSupported:o,orientation:r,angle:i,lockOrientation:u=>o.value&&typeof l.lock=="function"?l.lock(u):Promise.reject(new Error("Not supported")),unlockOrientation:()=>{o.value&&typeof l.unlock=="function"&&l.unlock()}}}function Co(e,t={}){const{deviceOrientationTiltAdjust:o=p=>p,deviceOrientationRollAdjust:l=p=>p,mouseTiltAdjust:r=p=>p,mouseRollAdjust:i=p=>p,window:s=L}=t,a=n.reactive(Ge({window:s})),u=n.reactive(rt({window:s})),{elementX:f,elementY:c,elementWidth:d,elementHeight:h}=ot(e,{handleOutside:!1,window:s}),m=n.computed(()=>a.isSupported&&(a.alpha!=null&&a.alpha!==0||a.gamma!=null&&a.gamma!==0)?"deviceOrientation":"mouse"),v=n.computed(()=>{if(m.value==="deviceOrientation"){let p;switch(u.orientation){case"landscape-primary":p=a.gamma/90;break;case"landscape-secondary":p=-a.gamma/90;break;case"portrait-primary":p=-a.beta/90;break;case"portrait-secondary":p=a.beta/90;break;default:p=-a.beta/90}return l(p)}else{const p=-(c.value-h.value/2)/h.value;return i(p)}}),S=n.computed(()=>{if(m.value==="deviceOrientation"){let p;switch(u.orientation){case"landscape-primary":p=a.beta/90;break;case"landscape-secondary":p=-a.beta/90;break;case"portrait-primary":p=a.gamma/90;break;case"portrait-secondary":p=-a.gamma/90;break;default:p=a.gamma/90}return o(p)}else{const p=(f.value-d.value/2)/d.value;return r(p)}});return{roll:v,tilt:S,source:m}}function Ao(e=je()){const t=n.shallowRef(),o=()=>{const l=W(e);l&&(t.value=l.parentElement)};return b.tryOnMounted(o),n.watch(()=>n.toValue(e),o),t}function Mo(e,t){const{window:o=L,immediate:l=!0,...r}=e,i=H(()=>o&&"PerformanceObserver"in o);let s;const a=()=>{s?.disconnect()},u=()=>{i.value&&(a(),s=new PerformanceObserver(t),s.observe(r))};return b.tryOnScopeDispose(a),l&&u(),{isSupported:i,start:u,stop:a}}const it={x:0,y:0,pointerId:0,pressure:0,tiltX:0,tiltY:0,width:0,height:0,twist:0,pointerType:null},Io=Object.keys(it);function Lo(e={}){const{target:t=L}=e,o=n.shallowRef(!1),l=n.ref(e.initialValue||{});Object.assign(l.value,it,l.value);const r=i=>{o.value=!0,!(e.pointerTypes&&!e.pointerTypes.includes(i.pointerType))&&(l.value=b.objectPick(i,Io,!1))};if(t){const i={passive:!0};k(t,["pointerdown","pointermove","pointerup"],r,i),k(t,"pointerleave",()=>o.value=!1,i)}return{...b.toRefs(l),isInside:o}}function No(e,t={}){const{document:o=q}=t,l=H(()=>o&&"pointerLockElement"in o),r=n.shallowRef(),i=n.shallowRef();let s;if(l.value){const f={passive:!0};k(o,"pointerlockchange",()=>{var c;const d=(c=o.pointerLockElement)!=null?c:r.value;s&&d===s&&(r.value=o.pointerLockElement,r.value||(s=i.value=null))},f),k(o,"pointerlockerror",()=>{var c;const d=(c=o.pointerLockElement)!=null?c:r.value;if(s&&d===s){const h=o.pointerLockElement?"release":"acquire";throw new Error(`Failed to ${h} pointer lock.`)}},f)}async function a(f){var c;if(!l.value)throw new Error("Pointer Lock API is not supported by your browser.");if(i.value=f instanceof Event?f.currentTarget:null,s=f instanceof Event?(c=W(e))!=null?c:i.value:W(f),!s)throw new Error("Target element undefined.");return s.requestPointerLock(),await b.until(r).toBe(s)}async function u(){return r.value?(o.exitPointerLock(),await b.until(r).toBeNull(),!0):!1}return{isSupported:l,element:r,triggerElement:i,lock:a,unlock:u}}function xo(e,t={}){const o=b.toRef(e),{threshold:l=50,onSwipe:r,onSwipeEnd:i,onSwipeStart:s,disableTextSelect:a=!1}=t,u=n.reactive({x:0,y:0}),f=(_,V)=>{u.x=_,u.y=V},c=n.reactive({x:0,y:0}),d=(_,V)=>{c.x=_,c.y=V},h=n.computed(()=>u.x-c.x),m=n.computed(()=>u.y-c.y),{max:v,abs:S}=Math,p=n.computed(()=>v(S(h.value),S(m.value))>=l),y=n.shallowRef(!1),w=n.shallowRef(!1),g=n.computed(()=>p.value?S(h.value)>S(m.value)?h.value>0?"left":"right":m.value>0?"up":"down":"none"),O=_=>{var V,C,M;const F=_.buttons===0,A=_.buttons===1;return(M=(C=(V=t.pointerTypes)==null?void 0:V.includes(_.pointerType))!=null?C:F||A)!=null?M:!0},E={passive:!0},P=[k(e,"pointerdown",_=>{if(!O(_))return;w.value=!0;const V=_.target;V?.setPointerCapture(_.pointerId);const{clientX:C,clientY:M}=_;f(C,M),d(C,M),s?.(_)},E),k(e,"pointermove",_=>{if(!O(_)||!w.value)return;const{clientX:V,clientY:C}=_;d(V,C),!y.value&&p.value&&(y.value=!0),y.value&&r?.(_)},E),k(e,"pointerup",_=>{O(_)&&(y.value&&i?.(_,g.value),w.value=!1,y.value=!1)},E)];b.tryOnMounted(()=>{var _,V,C,M,F,A,D,N;(V=(_=o.value)==null?void 0:_.style)==null||V.setProperty("touch-action","pan-y"),a&&((M=(C=o.value)==null?void 0:C.style)==null||M.setProperty("-webkit-user-select","none"),(A=(F=o.value)==null?void 0:F.style)==null||A.setProperty("-ms-user-select","none"),(N=(D=o.value)==null?void 0:D.style)==null||N.setProperty("user-select","none"))});const T=()=>P.forEach(_=>_());return{isSwiping:n.readonly(y),direction:n.readonly(g),posStart:n.readonly(u),posEnd:n.readonly(c),distanceX:h,distanceY:m,stop:T}}function Wo(e){const t=$("(prefers-color-scheme: light)",e),o=$("(prefers-color-scheme: dark)",e);return n.computed(()=>o.value?"dark":t.value?"light":"no-preference")}function Ho(e){const t=$("(prefers-contrast: more)",e),o=$("(prefers-contrast: less)",e),l=$("(prefers-contrast: custom)",e);return n.computed(()=>t.value?"more":o.value?"less":l.value?"custom":"no-preference")}function Uo(e={}){const{window:t=L}=e;if(!t)return n.ref(["en"]);const o=t.navigator,l=n.ref(o.languages);return k(t,"languagechange",()=>{l.value=o.languages},{passive:!0}),l}function $o(e){const t=$("(prefers-reduced-motion: reduce)",e);return n.computed(()=>t.value?"reduce":"no-preference")}function Bo(e){const t=$("(prefers-reduced-transparency: reduce)",e);return n.computed(()=>t.value?"reduce":"no-preference")}function jo(e,t){const o=n.shallowRef(t);return n.watch(b.toRef(e),(l,r)=>{o.value=r},{flush:"sync"}),n.readonly(o)}const st="--vueuse-safe-area-top",ut="--vueuse-safe-area-right",ct="--vueuse-safe-area-bottom",ft="--vueuse-safe-area-left";function zo(){const e=n.shallowRef(""),t=n.shallowRef(""),o=n.shallowRef(""),l=n.shallowRef("");if(b.isClient){const i=oe(st),s=oe(ut),a=oe(ct),u=oe(ft);i.value="env(safe-area-inset-top, 0px)",s.value="env(safe-area-inset-right, 0px)",a.value="env(safe-area-inset-bottom, 0px)",u.value="env(safe-area-inset-left, 0px)",b.tryOnMounted(r),k("resize",b.useDebounceFn(r),{passive:!0})}function r(){e.value=he(st),t.value=he(ut),o.value=he(ct),l.value=he(ft)}return{top:e,right:t,bottom:o,left:l,update:r}}function he(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function qo(e,t=b.noop,o={}){const{immediate:l=!0,manual:r=!1,type:i="text/javascript",async:s=!0,crossOrigin:a,referrerPolicy:u,noModule:f,defer:c,document:d=q,attrs:h={},nonce:m=void 0}=o,v=n.shallowRef(null);let S=null;const p=g=>new Promise((O,E)=>{const P=C=>(v.value=C,O(C),C);if(!d){O(!1);return}let T=!1,_=d.querySelector(`script[src="${n.toValue(e)}"]`);_?_.hasAttribute("data-loaded")&&P(_):(_=d.createElement("script"),_.type=i,_.async=s,_.src=n.toValue(e),c&&(_.defer=c),a&&(_.crossOrigin=a),f&&(_.noModule=f),u&&(_.referrerPolicy=u),m&&(_.nonce=m),Object.entries(h).forEach(([C,M])=>_?.setAttribute(C,M)),T=!0);const V={passive:!0};k(_,"error",C=>E(C),V),k(_,"abort",C=>E(C),V),k(_,"load",()=>{_.setAttribute("data-loaded","true"),t(_),P(_)},V),T&&(_=d.head.appendChild(_)),g||P(_)}),y=(g=!0)=>(S||(S=p(g)),S),w=()=>{if(!d)return;S=null,v.value&&(v.value=null);const g=d.querySelector(`script[src="${n.toValue(e)}"]`);g&&d.head.removeChild(g)};return l&&!r&&b.tryOnMounted(y),r||b.tryOnUnmounted(w),{scriptTag:v,load:y,unload:w}}function dt(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const o=e.parentNode;return!o||o.tagName==="BODY"?!1:dt(o)}}function Go(e){const t=e||window.event,o=t.target;return dt(o)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const Fe=new WeakMap;function Yo(e,t=!1){const o=n.shallowRef(t);let l=null,r="";n.watch(b.toRef(e),a=>{const u=pe(n.toValue(a));if(u){const f=u;if(Fe.get(f)||Fe.set(f,f.style.overflow),f.style.overflow!=="hidden"&&(r=f.style.overflow),f.style.overflow==="hidden")return o.value=!0;if(o.value)return f.style.overflow="hidden"}},{immediate:!0});const i=()=>{const a=pe(n.toValue(e));!a||o.value||(b.isIOS&&(l=k(a,"touchmove",u=>{Go(u)},{passive:!1})),a.style.overflow="hidden",o.value=!0)},s=()=>{const a=pe(n.toValue(e));!a||!o.value||(b.isIOS&&l?.(),a.style.overflow=r,Fe.delete(a),o.value=!1)};return b.tryOnScopeDispose(s),n.computed({get(){return o.value},set(a){a?i():s()}})}function Xo(e,t,o={}){const{window:l=L}=o;return me(e,t,l?.sessionStorage,o)}function Ko(e={},t={}){const{navigator:o=G}=t,l=o,r=H(()=>l&&"canShare"in l);return{isSupported:r,share:async(s={})=>{if(r.value){const a={...n.toValue(e),...n.toValue(s)};let u=!0;if(a.files&&l.canShare&&(u=l.canShare({files:a.files})),u)return l.share(a)}}}}const Jo=(e,t)=>e.sort(t),ye=(e,t)=>e-t;function Qo(...e){var t,o,l,r;const[i]=e;let s=ye,a={};e.length===2?typeof e[1]=="object"?(a=e[1],s=(t=a.compareFn)!=null?t:ye):s=(o=e[1])!=null?o:ye:e.length>2&&(s=(l=e[1])!=null?l:ye,a=(r=e[2])!=null?r:{});const{dirty:u=!1,sortFn:f=Jo}=a;return u?(n.watchEffect(()=>{const c=f(n.toValue(i),s);n.isRef(i)?i.value=c:i.splice(0,i.length,...c)}),i):n.computed(()=>f([...n.toValue(i)],s))}function Zo(e={}){const{interimResults:t=!0,continuous:o=!0,maxAlternatives:l=1,window:r=L}=e,i=b.toRef(e.lang||"en-US"),s=n.shallowRef(!1),a=n.shallowRef(!1),u=n.shallowRef(""),f=n.shallowRef(void 0);let c;const d=()=>{s.value=!0},h=()=>{s.value=!1},m=(p=!s.value)=>{p?d():h()},v=r&&(r.SpeechRecognition||r.webkitSpeechRecognition),S=H(()=>v);return S.value&&(c=new v,c.continuous=o,c.interimResults=t,c.lang=n.toValue(i),c.maxAlternatives=l,c.onstart=()=>{s.value=!0,a.value=!1},n.watch(i,p=>{c&&!s.value&&(c.lang=p)}),c.onresult=p=>{const y=p.results[p.resultIndex],{transcript:w}=y[0];a.value=y.isFinal,u.value=w,f.value=void 0},c.onerror=p=>{f.value=p},c.onend=()=>{s.value=!1,c.lang=n.toValue(i)},n.watch(s,(p,y)=>{p!==y&&(p?c.start():c.stop())})),b.tryOnScopeDispose(()=>{h()}),{isSupported:S,isListening:s,isFinal:a,recognition:c,result:u,error:f,toggle:m,start:d,stop:h}}function el(e,t={}){const{pitch:o=1,rate:l=1,volume:r=1,window:i=L,onBoundary:s}=t,a=i&&i.speechSynthesis,u=H(()=>a),f=n.shallowRef(!1),c=n.shallowRef("init"),d=b.toRef(e||""),h=b.toRef(t.lang||"en-US"),m=n.shallowRef(void 0),v=(g=!f.value)=>{f.value=g},S=g=>{g.lang=n.toValue(h),g.voice=n.toValue(t.voice)||null,g.pitch=n.toValue(o),g.rate=n.toValue(l),g.volume=n.toValue(r),g.onstart=()=>{f.value=!0,c.value="play"},g.onpause=()=>{f.value=!1,c.value="pause"},g.onresume=()=>{f.value=!0,c.value="play"},g.onend=()=>{f.value=!1,c.value="end"},g.onerror=O=>{m.value=O},g.onboundary=O=>{s?.(O)}},p=n.computed(()=>{f.value=!1,c.value="init";const g=new SpeechSynthesisUtterance(d.value);return S(g),g}),y=()=>{a.cancel(),p&&a.speak(p.value)},w=()=>{a.cancel(),f.value=!1};return u.value&&(S(p.value),n.watch(h,g=>{p.value&&!f.value&&(p.value.lang=g)}),t.voice&&n.watch(t.voice,()=>{a.cancel()}),n.watch(f,()=>{f.value?a.resume():a.pause()})),b.tryOnScopeDispose(()=>{f.value=!1}),{isSupported:u,isPlaying:f,status:c,utterance:p,error:m,stop:w,toggle:v,speak:y}}function tl(e,t){const o=n.ref(e),l=n.computed(()=>Array.isArray(o.value)?o.value:Object.keys(o.value)),r=n.ref(l.value.indexOf(t??l.value[0])),i=n.computed(()=>c(r.value)),s=n.computed(()=>r.value===0),a=n.computed(()=>r.value===l.value.length-1),u=n.computed(()=>l.value[r.value+1]),f=n.computed(()=>l.value[r.value-1]);function c(E){return Array.isArray(o.value)?o.value[E]:o.value[l.value[E]]}function d(E){if(l.value.includes(E))return c(l.value.indexOf(E))}function h(E){l.value.includes(E)&&(r.value=l.value.indexOf(E))}function m(){a.value||r.value++}function v(){s.value||r.value--}function S(E){O(E)&&h(E)}function p(E){return l.value.indexOf(E)===r.value+1}function y(E){return l.value.indexOf(E)===r.value-1}function w(E){return l.value.indexOf(E)===r.value}function g(E){return r.value<l.value.indexOf(E)}function O(E){return r.value>l.value.indexOf(E)}return{steps:o,stepNames:l,index:r,current:i,next:u,previous:f,isFirst:s,isLast:a,at:c,get:d,goTo:h,goToNext:m,goToPrevious:v,goBackTo:S,isNext:p,isPrevious:y,isCurrent:w,isBefore:g,isAfter:O}}function nl(e,t,o,l={}){var r;const{flush:i="pre",deep:s=!0,listenToStorageChanges:a=!0,writeDefaults:u=!0,mergeDefaults:f=!1,shallow:c,window:d=L,eventFilter:h,onError:m=E=>{console.error(E)},onReady:v}=l,S=n.toValue(t),p=$e(S),y=(c?n.shallowRef:n.ref)(n.toValue(t)),w=(r=l.serializer)!=null?r:Re[p];if(!o)try{o=de("getDefaultStorageAsync",()=>{var E;return(E=L)==null?void 0:E.localStorage})()}catch(E){m(E)}async function g(E){if(!(!o||E&&E.key!==e))try{const P=E?E.newValue:await o.getItem(e);if(P==null)y.value=S,u&&S!==null&&await o.setItem(e,await w.write(S));else if(f){const T=await w.read(P);typeof f=="function"?y.value=f(T,S):p==="object"&&!Array.isArray(T)?y.value={...S,...T}:y.value=T}else y.value=await w.read(P)}catch(P){m(P)}}const O=new Promise(E=>{g().then(()=>{v?.(y.value),E(y)})});return d&&a&&k(d,"storage",E=>Promise.resolve().then(()=>g(E)),{passive:!0}),o&&b.watchWithFilter(y,async()=>{try{y.value==null?await o.removeItem(e):await o.setItem(e,await w.write(y.value))}catch(E){m(E)}},{flush:i,deep:s,eventFilter:h}),Object.assign(y,{then:O.then.bind(O),catch:O.catch.bind(O)}),y}let ol=0;function ll(e,t={}){const o=n.shallowRef(!1),{document:l=q,immediate:r=!0,manual:i=!1,id:s=`vueuse_styletag_${++ol}`}=t,a=n.shallowRef(e);let u=()=>{};const f=()=>{if(!l)return;const d=l.getElementById(s)||l.createElement("style");d.isConnected||(d.id=s,t.nonce&&(d.nonce=t.nonce),t.media&&(d.media=t.media),l.head.appendChild(d)),!o.value&&(u=n.watch(a,h=>{d.textContent=h},{immediate:!0}),o.value=!0)},c=()=>{!l||!o.value||(u(),l.head.removeChild(l.getElementById(s)),o.value=!1)};return r&&!i&&b.tryOnMounted(f),i||b.tryOnScopeDispose(c),{id:s,css:a,unload:c,load:f,isLoaded:n.readonly(o)}}function al(e,t={}){const{threshold:o=50,onSwipe:l,onSwipeEnd:r,onSwipeStart:i,passive:s=!0}=t,a=n.reactive({x:0,y:0}),u=n.reactive({x:0,y:0}),f=n.computed(()=>a.x-u.x),c=n.computed(()=>a.y-u.y),{max:d,abs:h}=Math,m=n.computed(()=>d(h(f.value),h(c.value))>=o),v=n.shallowRef(!1),S=n.computed(()=>m.value?h(f.value)>h(c.value)?f.value>0?"left":"right":c.value>0?"up":"down":"none"),p=T=>[T.touches[0].clientX,T.touches[0].clientY],y=(T,_)=>{a.x=T,a.y=_},w=(T,_)=>{u.x=T,u.y=_},g={passive:s,capture:!s},O=T=>{v.value&&r?.(T,S.value),v.value=!1},E=[k(e,"touchstart",T=>{if(T.touches.length!==1)return;const[_,V]=p(T);y(_,V),w(_,V),i?.(T)},g),k(e,"touchmove",T=>{if(T.touches.length!==1)return;const[_,V]=p(T);w(_,V),g.capture&&!g.passive&&Math.abs(f.value)>Math.abs(c.value)&&T.preventDefault(),!v.value&&m.value&&(v.value=!0),v.value&&l?.(T)},g),k(e,["touchend","touchcancel"],O,g)];return{isSwiping:v,direction:S,coordsStart:a,coordsEnd:u,lengthX:f,lengthY:c,stop:()=>E.forEach(T=>T()),isPassiveEventSupported:!0}}function rl(){const e=n.ref([]);return e.value.set=t=>{t&&e.value.push(t)},n.onBeforeUpdate(()=>{e.value.length=0}),e}function il(e={}){const{document:t=q,selector:o="html",observe:l=!1,initialValue:r="ltr"}=e;function i(){var a,u;return(u=(a=t?.querySelector(o))==null?void 0:a.getAttribute("dir"))!=null?u:r}const s=n.ref(i());return b.tryOnMounted(()=>s.value=i()),l&&t&&X(t.querySelector(o),()=>s.value=i(),{attributes:!0}),n.computed({get(){return s.value},set(a){var u,f;s.value=a,t&&(s.value?(u=t.querySelector(o))==null||u.setAttribute("dir",s.value):(f=t.querySelector(o))==null||f.removeAttribute("dir"))}})}function sl(e){var t;const o=(t=e.rangeCount)!=null?t:0;return Array.from({length:o},(l,r)=>e.getRangeAt(r))}function ul(e={}){const{window:t=L}=e,o=n.ref(null),l=n.computed(()=>{var a,u;return(u=(a=o.value)==null?void 0:a.toString())!=null?u:""}),r=n.computed(()=>o.value?sl(o.value):[]),i=n.computed(()=>r.value.map(a=>a.getBoundingClientRect()));function s(){o.value=null,t&&(o.value=t.getSelection())}return t&&k(t.document,"selectionchange",s,{passive:!0}),{text:l,rects:i,ranges:r,selection:o}}function cl(e=L,t){e&&typeof e.requestAnimationFrame=="function"?e.requestAnimationFrame(t):t()}function fl(e={}){var t,o;const{window:l=L}=e,r=b.toRef(e?.element),i=b.toRef((t=e?.input)!=null?t:""),s=(o=e?.styleProp)!=null?o:"height",a=n.shallowRef(1),u=n.shallowRef(0);function f(){var c;if(!r.value)return;let d="";r.value.style[s]="1px",a.value=(c=r.value)==null?void 0:c.scrollHeight;const h=n.toValue(e?.styleTarget);h?h.style[s]=`${a.value}px`:d=`${a.value}px`,r.value.style[s]=d}return n.watch([i,r],()=>n.nextTick(f),{immediate:!0}),n.watch(a,()=>{var c;return(c=e?.onResize)==null?void 0:c.call(e)}),le(r,([{contentRect:c}])=>{u.value!==c.width&&cl(l,()=>{u.value=c.width,f()})}),e?.watch&&n.watch(e.watch,f,{immediate:!0,deep:!0}),{textarea:r,input:i,triggerResize:f}}function dl(e,t={}){const{throttle:o=200,trailing:l=!0}=t,r=b.throttleFilter(o,l);return{...Te(e,{...t,eventFilter:r})}}const ml=[{max:6e4,value:1e3,name:"second"},{max:276e4,value:6e4,name:"minute"},{max:72e6,value:36e5,name:"hour"},{max:5184e5,value:864e5,name:"day"},{max:24192e5,value:6048e5,name:"week"},{max:28512e6,value:2592e6,name:"month"},{max:Number.POSITIVE_INFINITY,value:31536e6,name:"year"}],vl={justNow:"just now",past:e=>e.match(/\d/)?`${e} ago`:e,future:e=>e.match(/\d/)?`in ${e}`:e,month:(e,t)=>e===1?t?"last month":"next month":`${e} month${e>1?"s":""}`,year:(e,t)=>e===1?t?"last year":"next year":`${e} year${e>1?"s":""}`,day:(e,t)=>e===1?t?"yesterday":"tomorrow":`${e} day${e>1?"s":""}`,week:(e,t)=>e===1?t?"last week":"next week":`${e} week${e>1?"s":""}`,hour:e=>`${e} hour${e>1?"s":""}`,minute:e=>`${e} minute${e>1?"s":""}`,second:e=>`${e} second${e>1?"s":""}`,invalid:""};function pl(e){return e.toISOString().slice(0,10)}function hl(e,t={}){const{controls:o=!1,updateInterval:l=3e4}=t,{now:r,...i}=Ve({interval:l,controls:!0}),s=n.computed(()=>mt(new Date(n.toValue(e)),t,n.toValue(r)));return o?{timeAgo:s,...i}:s}function mt(e,t={},o=Date.now()){var l;const{max:r,messages:i=vl,fullDateFormatter:s=pl,units:a=ml,showSecond:u=!1,rounding:f="round"}=t,c=typeof f=="number"?p=>+p.toFixed(f):Math[f],d=+o-+e,h=Math.abs(d);function m(p,y){return c(Math.abs(p)/y.value)}function v(p,y){const w=m(p,y),g=p>0,O=S(y.name,w,g);return S(g?"past":"future",O,g)}function S(p,y,w){const g=i[p];return typeof g=="function"?g(y,w):g.replace("{0}",y.toString())}if(h<6e4&&!u)return i.justNow;if(typeof r=="number"&&h>r)return s(new Date(e));if(typeof r=="string"){const p=(l=a.find(y=>y.name===r))==null?void 0:l.max;if(p&&h>p)return s(new Date(e))}for(const[p,y]of a.entries()){if(m(d,y)<=0&&a[p-1])return v(d,a[p-1]);if(h<y.max)return v(d,y)}return i.invalid}const yl=[{name:"year",ms:31536e6},{name:"month",ms:2592e6},{name:"week",ms:6048e5},{name:"day",ms:864e5},{name:"hour",ms:36e5},{name:"minute",ms:6e4},{name:"second",ms:1e3}];function wl(e,t={}){const{controls:o=!1,updateInterval:l=3e4}=t,{now:r,...i}=Ve({interval:l,controls:!0}),s=n.computed(()=>vt(new Date(n.toValue(e)),t,n.toValue(r))),a=n.computed(()=>s.value.parts),u=n.computed(()=>Pe(a.value,{...t,locale:s.value.resolvedLocale}));return o?{timeAgoIntl:u,parts:a,...i}:u}function gl(e,t={},o=Date.now()){const{parts:l,resolvedLocale:r}=vt(e,t,o);return Pe(l,{...t,locale:r})}function vt(e,t={},o=Date.now()){const{locale:l,relativeTimeFormatOptions:r={numeric:"auto"}}=t,i=new Intl.RelativeTimeFormat(l,r),{locale:s}=i.resolvedOptions(),a=+e-+o,u=Math.abs(a);for(const{name:f,ms:c}of yl)if(u>=c)return{resolvedLocale:s,parts:i.formatToParts(Math.round(a/c),f)};return{resolvedLocale:s,parts:i.formatToParts(0,"second")}}function Pe(e,t={}){const{insertSpace:o=!0,joinParts:l,locale:r}=t;return typeof l=="function"?l(e,r):o?e.map(i=>i.value.trim()).join(" "):e.map(i=>i.value).join("")}function bl(e,t,o={}){const{immediate:l=!0,immediateCallback:r=!1}=o,{start:i}=b.useTimeoutFn(a,t,{immediate:l}),s=n.shallowRef(!1);async function a(){s.value&&(await e(),i())}function u(){s.value||(s.value=!0,r&&e(),i())}function f(){s.value=!1}return l&&b.isClient&&u(),b.tryOnScopeDispose(f),{isActive:s,pause:f,resume:u}}function Sl(e={}){const{controls:t=!1,offset:o=0,immediate:l=!0,interval:r="requestAnimationFrame",callback:i}=e,s=n.shallowRef(b.timestamp()+o),a=()=>s.value=b.timestamp()+o,u=i?()=>{a(),i(s.value)}:a,f=r==="requestAnimationFrame"?K(u,{immediate:l}):b.useIntervalFn(u,r,{immediate:l});return t?{timestamp:s,...f}:s}function Rl(e=null,t={}){var o,l,r;const{document:i=q,restoreOnUnmount:s=d=>d}=t,a=(o=i?.title)!=null?o:"",u=b.toRef((l=e??i?.title)!=null?l:null),f=!!(e&&typeof e=="function");function c(d){if(!("titleTemplate"in t))return d;const h=t.titleTemplate||"%s";return typeof h=="function"?h(d):n.toValue(h).replace(/%s/g,d)}return n.watch(u,(d,h)=>{d!==h&&i&&(i.title=c(d??""))},{immediate:!0}),t.observe&&!t.titleTemplate&&i&&!f&&X((r=i.head)==null?void 0:r.querySelector("title"),()=>{i&&i.title!==u.value&&(u.value=c(i.title))},{childList:!0}),b.tryOnScopeDispose(()=>{if(s){const d=s(a,u.value||"");d!=null&&i&&(i.title=d)}}),u}const El={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},Tl=Object.assign({},{linear:b.identity},El);function Ol([e,t,o,l]){const r=(c,d)=>1-3*d+3*c,i=(c,d)=>3*d-6*c,s=c=>3*c,a=(c,d,h)=>((r(d,h)*c+i(d,h))*c+s(d))*c,u=(c,d,h)=>3*r(d,h)*c*c+2*i(d,h)*c+s(d),f=c=>{let d=c;for(let h=0;h<4;++h){const m=u(d,e,o);if(m===0)return d;const v=a(d,e,o)-c;d-=v/m}return d};return c=>e===t&&o===l?c:a(f(c),t,l)}function pt(e,t,o){return e+o*(t-e)}function De(e){return(typeof e=="number"?[e]:e)||[]}function ht(e,t,o,l={}){var r,i;const{window:s=L}=l,a=n.toValue(t),u=n.toValue(o),f=De(a),c=De(u),d=(r=n.toValue(l.duration))!=null?r:1e3,h=Date.now(),m=Date.now()+d,v=typeof l.transition=="function"?l.transition:(i=n.toValue(l.transition))!=null?i:b.identity,S=typeof v=="function"?v:Ol(v);return new Promise(p=>{e.value=a;const y=()=>{var w;if((w=l.abort)!=null&&w.call(l)){p();return}const g=Date.now(),O=S((g-h)/d),E=De(e.value).map((P,T)=>pt(f[T],c[T],O));Array.isArray(e.value)?e.value=E.map((P,T)=>{var _,V;return pt((_=f[T])!=null?_:0,(V=c[T])!=null?V:0,O)}):typeof e.value=="number"&&(e.value=E[0]),g<m?s?.requestAnimationFrame(y):(e.value=u,p())};y()})}function kl(e,t={}){let o=0;const l=()=>{const i=n.toValue(e);return typeof i=="number"?i:i.map(n.toValue)},r=n.ref(l());return n.watch(l,async i=>{var s,a;if(n.toValue(t.disabled))return;const u=++o;if(t.delay&&await b.promiseTimeout(n.toValue(t.delay)),u!==o)return;const f=Array.isArray(i)?i.map(n.toValue):n.toValue(i);(s=t.onStarted)==null||s.call(t),await ht(r,r.value,f,{...t,abort:()=>{var c;return u!==o||((c=t.abort)==null?void 0:c.call(t))}}),(a=t.onFinished)==null||a.call(t)},{deep:!0}),n.watch(()=>n.toValue(t.disabled),i=>{i&&(o++,r.value=l())}),b.tryOnScopeDispose(()=>{o++}),n.computed(()=>n.toValue(t.disabled)?l():r.value)}function _l(e="history",t={}){const{initialValue:o={},removeNullishValues:l=!0,removeFalsyValues:r=!1,write:i=!0,writeMode:s="replace",window:a=L,stringify:u=O=>O.toString()}=t;if(!a)return n.reactive(o);const f=n.reactive({});function c(){if(e==="history")return a.location.search||"";if(e==="hash"){const O=a.location.hash||"",E=O.indexOf("?");return E>0?O.slice(E):""}else return(a.location.hash||"").replace(/^#/,"")}function d(O){const E=u(O);if(e==="history")return`${E?`?${E}`:""}${a.location.hash||""}`;if(e==="hash-params")return`${a.location.search||""}${E?`#${E}`:""}`;const P=a.location.hash||"#",T=P.indexOf("?");return T>0?`${a.location.search||""}${P.slice(0,T)}${E?`?${E}`:""}`:`${a.location.search||""}${P}${E?`?${E}`:""}`}function h(){return new URLSearchParams(c())}function m(O){const E=new Set(Object.keys(f));for(const P of O.keys()){const T=O.getAll(P);f[P]=T.length>1?T:O.get(P)||"",E.delete(P)}Array.from(E).forEach(P=>delete f[P])}const{pause:v,resume:S}=b.pausableWatch(f,()=>{const O=new URLSearchParams("");Object.keys(f).forEach(E=>{const P=f[E];Array.isArray(P)?P.forEach(T=>O.append(E,T)):l&&P==null||r&&!P?O.delete(E):O.set(E,P)}),p(O,!1)},{deep:!0});function p(O,E){v(),E&&m(O),s==="replace"?a.history.replaceState(a.history.state,a.document.title,a.location.pathname+d(O)):a.history.pushState(a.history.state,a.document.title,a.location.pathname+d(O)),S()}function y(){i&&p(h(),!0)}const w={passive:!0};k(a,"popstate",y,w),e!=="history"&&k(a,"hashchange",y,w);const g=h();return g.keys().next().value?m(g):Object.assign(f,o),f}function Vl(e={}){var t,o;const l=n.shallowRef((t=e.enabled)!=null?t:!1),r=n.shallowRef((o=e.autoSwitch)!=null?o:!0),i=n.ref(e.constraints),{navigator:s=G}=e,a=H(()=>{var S;return(S=s?.mediaDevices)==null?void 0:S.getUserMedia}),u=n.shallowRef();function f(S){switch(S){case"video":{if(i.value)return i.value.video||!1;break}case"audio":{if(i.value)return i.value.audio||!1;break}}}async function c(){if(!(!a.value||u.value))return u.value=await s.mediaDevices.getUserMedia({video:f("video"),audio:f("audio")}),u.value}function d(){var S;(S=u.value)==null||S.getTracks().forEach(p=>p.stop()),u.value=void 0}function h(){d(),l.value=!1}async function m(){return await c(),u.value&&(l.value=!0),u.value}async function v(){return d(),await m()}return n.watch(l,S=>{S?c():d()},{immediate:!0}),n.watch(i,()=>{r.value&&u.value&&v()},{immediate:!0}),b.tryOnScopeDispose(()=>{h()}),{isSupported:a,stream:u,start:m,stop:h,restart:v,constraints:i,enabled:l,autoSwitch:r}}function yt(e,t,o,l={}){var r,i,s;const{clone:a=!1,passive:u=!1,eventName:f,deep:c=!1,defaultValue:d,shouldEmit:h}=l,m=n.getCurrentInstance(),v=o||m?.emit||((r=m?.$emit)==null?void 0:r.bind(m))||((s=(i=m?.proxy)==null?void 0:i.$emit)==null?void 0:s.bind(m?.proxy));let S=f;t||(t="modelValue"),S=S||`update:${t.toString()}`;const p=g=>a?typeof a=="function"?a(g):ne(g):g,y=()=>b.isDef(e[t])?p(e[t]):d,w=g=>{h?h(g)&&v(S,g):v(S,g)};if(u){const g=y(),O=n.ref(g);let E=!1;return n.watch(()=>e[t],P=>{E||(E=!0,O.value=p(P),n.nextTick(()=>E=!1))}),n.watch(O,P=>{!E&&(P!==e[t]||c)&&w(P)},{deep:c}),O}else return n.computed({get(){return y()},set(g){w(g)}})}function Fl(e,t,o={}){const l={};for(const r in e)l[r]=yt(e,r,t,o);return l}function Pl(e){const{pattern:t=[],interval:o=0,navigator:l=G}=e||{},r=H(()=>typeof l<"u"&&"vibrate"in l),i=b.toRef(t);let s;const a=(f=i.value)=>{r.value&&l.vibrate(f)},u=()=>{r.value&&l.vibrate(0),s?.pause()};return o>0&&(s=b.useIntervalFn(a,o,{immediate:!1,immediateCallback:!1})),{isSupported:r,pattern:t,intervalControls:s,vibrate:a,stop:u}}function Dl(e,t){const{containerStyle:o,wrapperProps:l,scrollTo:r,calculateRange:i,currentList:s,containerRef:a}="itemHeight"in t?Ml(t,e):Al(t,e);return{list:s,scrollTo:r,containerProps:{ref:a,onScroll:()=>{i()},style:o},wrapperProps:l}}function wt(e){const t=n.shallowRef(null),o=Xe(t),l=n.ref([]),r=n.shallowRef(e);return{state:n.ref({start:0,end:10}),source:r,currentList:l,size:o,containerRef:t}}function gt(e,t,o){return l=>{if(typeof o=="number")return Math.ceil(l/o);const{start:r=0}=e.value;let i=0,s=0;for(let a=r;a<t.value.length;a++){const u=o(a);if(i+=u,s=a,i>l)break}return s-r}}function bt(e,t){return o=>{if(typeof t=="number")return Math.floor(o/t)+1;let l=0,r=0;for(let i=0;i<e.value.length;i++){const s=t(i);if(l+=s,l>=o){r=i;break}}return r+1}}function St(e,t,o,l,{containerRef:r,state:i,currentList:s,source:a}){return()=>{const u=r.value;if(u){const f=o(e==="vertical"?u.scrollTop:u.scrollLeft),c=l(e==="vertical"?u.clientHeight:u.clientWidth),d=f-t,h=f+c+t;i.value={start:d<0?0:d,end:h>a.value.length?a.value.length:h},s.value=a.value.slice(i.value.start,i.value.end).map((m,v)=>({data:m,index:v+i.value.start}))}}}function Rt(e,t){return o=>typeof e=="number"?o*e:t.value.slice(0,o).reduce((r,i,s)=>r+e(s),0)}function Et(e,t,o,l){n.watch([e.width,e.height,()=>n.toValue(t),o],()=>{l()})}function Tt(e,t){return n.computed(()=>typeof e=="number"?t.value.length*e:t.value.reduce((o,l,r)=>o+e(r),0))}const Cl={horizontal:"scrollLeft",vertical:"scrollTop"};function Ot(e,t,o,l){return r=>{l.value&&(l.value[Cl[e]]=o(r),t())}}function Al(e,t){const o=wt(t),{state:l,source:r,currentList:i,size:s,containerRef:a}=o,u={overflowX:"auto"},{itemWidth:f,overscan:c=5}=e,d=gt(l,r,f),h=bt(r,f),m=St("horizontal",c,h,d,o),v=Rt(f,r),S=n.computed(()=>v(l.value.start)),p=Tt(f,r);Et(s,t,a,m);const y=Ot("horizontal",m,v,a),w=n.computed(()=>({style:{height:"100%",width:`${p.value-S.value}px`,marginLeft:`${S.value}px`,display:"flex"}}));return{scrollTo:y,calculateRange:m,wrapperProps:w,containerStyle:u,currentList:i,containerRef:a}}function Ml(e,t){const o=wt(t),{state:l,source:r,currentList:i,size:s,containerRef:a}=o,u={overflowY:"auto"},{itemHeight:f,overscan:c=5}=e,d=gt(l,r,f),h=bt(r,f),m=St("vertical",c,h,d,o),v=Rt(f,r),S=n.computed(()=>v(l.value.start)),p=Tt(f,r);Et(s,t,a,m);const y=Ot("vertical",m,v,a),w=n.computed(()=>({style:{width:"100%",height:`${p.value-S.value}px`,marginTop:`${S.value}px`}}));return{calculateRange:m,scrollTo:y,containerStyle:u,wrapperProps:w,currentList:i,containerRef:a}}function Il(e={}){const{navigator:t=G,document:o=q}=e,l=n.shallowRef(!1),r=n.shallowRef(null),i=Ye({document:o}),s=H(()=>t&&"wakeLock"in t),a=n.computed(()=>!!r.value&&i.value==="visible");s.value&&(k(r,"release",()=>{var d,h;l.value=(h=(d=r.value)==null?void 0:d.type)!=null?h:!1},{passive:!0}),b.whenever(()=>i.value==="visible"&&o?.visibilityState==="visible"&&l.value,d=>{l.value=!1,u(d)}));async function u(d){var h;await((h=r.value)==null?void 0:h.release()),r.value=s.value?await t.wakeLock.request(d):null}async function f(d){i.value==="visible"?await u(d):l.value=d}async function c(){l.value=!1;const d=r.value;r.value=null,await d?.release()}return{sentinel:r,isSupported:s,isActive:a,request:f,forceRequest:u,release:c}}function Ll(e={}){const{window:t=L,requestPermissions:o=!0}=e,l=e,r=H(()=>{if(!t||!("Notification"in t))return!1;if(Notification.permission==="granted")return!0;try{const w=new Notification("");w.onshow=()=>{w.close()}}catch(w){if(w.name==="TypeError")return!1}return!0}),i=n.shallowRef(r.value&&"permission"in Notification&&Notification.permission==="granted"),s=n.ref(null),a=async()=>{if(r.value)return!i.value&&Notification.permission!=="denied"&&await Notification.requestPermission()==="granted"&&(i.value=!0),i.value},{on:u,trigger:f}=b.createEventHook(),{on:c,trigger:d}=b.createEventHook(),{on:h,trigger:m}=b.createEventHook(),{on:v,trigger:S}=b.createEventHook(),p=async w=>{if(!r.value||!i.value)return;const g=Object.assign({},l,w);return s.value=new Notification(g.title||"",g),s.value.onclick=f,s.value.onshow=d,s.value.onerror=m,s.value.onclose=S,s.value},y=()=>{s.value&&s.value.close(),s.value=null};if(o&&b.tryOnMounted(a),b.tryOnScopeDispose(y),r.value&&t){const w=t.document;k(w,"visibilitychange",g=>{g.preventDefault(),w.visibilityState==="visible"&&y()})}return{isSupported:r,notification:s,ensurePermissions:a,permissionGranted:i,show:p,close:y,onClick:u,onShow:c,onError:h,onClose:v}}const kt="ping";function Ce(e){return e===!0?{}:e}function Nl(e,t={}){const{onConnected:o,onDisconnected:l,onError:r,onMessage:i,immediate:s=!0,autoConnect:a=!0,autoClose:u=!0,protocols:f=[]}=t,c=n.ref(null),d=n.shallowRef("CLOSED"),h=n.ref(),m=b.toRef(e);let v,S,p=!1,y=0,w=[],g,O;const E=()=>{if(w.length&&h.value&&d.value==="OPEN"){for(const F of w)h.value.send(F);w=[]}},P=()=>{g!=null&&(clearTimeout(g),g=void 0)},T=()=>{clearTimeout(O),O=void 0},_=(F=1e3,A)=>{P(),!(!b.isClient&&!b.isWorker||!h.value)&&(p=!0,T(),v?.(),h.value.close(F,A),h.value=void 0)},V=(F,A=!0)=>!h.value||d.value!=="OPEN"?(A&&w.push(F),!1):(E(),h.value.send(F),!0),C=()=>{if(p||typeof m.value>"u")return;const F=new WebSocket(m.value,f);h.value=F,d.value="CONNECTING",F.onopen=()=>{d.value="OPEN",y=0,o?.(F),S?.(),E()},F.onclose=A=>{if(d.value="CLOSED",T(),v?.(),l?.(F,A),!p&&t.autoReconnect&&(h.value==null||F===h.value)){const{retries:D=-1,delay:N=1e3,onFailed:x}=Ce(t.autoReconnect);(typeof D=="function"?D:()=>typeof D=="number"&&(D<0||y<D))(y)?(y+=1,g=setTimeout(C,N)):x?.()}},F.onerror=A=>{r?.(F,A)},F.onmessage=A=>{if(t.heartbeat){T();const{message:D=kt,responseMessage:N=D}=Ce(t.heartbeat);if(A.data===n.toValue(N))return}c.value=A.data,i?.(F,A)}};if(t.heartbeat){const{message:F=kt,interval:A=1e3,pongTimeout:D=1e3}=Ce(t.heartbeat),{pause:N,resume:x}=b.useIntervalFn(()=>{V(n.toValue(F),!1),O==null&&(O=setTimeout(()=>{_(),p=!1},D))},A,{immediate:!1});v=N,S=x}u&&(b.isClient&&k("beforeunload",()=>_(),{passive:!0}),b.tryOnScopeDispose(_));const M=()=>{!b.isClient&&!b.isWorker||(_(),p=!1,y=0,C())};return s&&M(),a&&n.watch(m,M),{data:c,status:d,close:_,send:V,open:M,ws:h}}function xl(e,t,o){const{window:l=L}=o??{},r=n.ref(null),i=n.shallowRef(),s=(...u)=>{i.value&&i.value.postMessage(...u)},a=function(){i.value&&i.value.terminate()};return l&&(typeof e=="string"?i.value=new Worker(e,t):typeof e=="function"?i.value=e():i.value=e,i.value.onmessage=u=>{r.value=u.data},b.tryOnScopeDispose(()=>{i.value&&i.value.terminate()})),{data:r,post:s,terminate:a,worker:i}}function Wl(e,t){if(e.length===0&&t.length===0)return"";const o=e.map(i=>`'${i}'`).toString(),l=t.filter(i=>typeof i=="function").map(i=>{const s=i.toString();return s.trim().startsWith("function")?s:`const ${i.name} = ${s}`}).join(";"),r=`importScripts(${o});`;return`${o.trim()===""?"":r} ${l}`}function Hl(e){return t=>{const o=t.data[0];return Promise.resolve(e.apply(void 0,o)).then(l=>{postMessage(["SUCCESS",l])}).catch(l=>{postMessage(["ERROR",l])})}}function Ul(e,t,o){const l=`${Wl(t,o)}; onmessage=(${Hl})(${e})`,r=new Blob([l],{type:"text/javascript"});return URL.createObjectURL(r)}function $l(e,t={}){const{dependencies:o=[],localDependencies:l=[],timeout:r,window:i=L}=t,s=n.ref(),a=n.shallowRef("PENDING"),u=n.ref({}),f=n.shallowRef(),c=(v="PENDING")=>{s.value&&s.value._url&&i&&(s.value.terminate(),URL.revokeObjectURL(s.value._url),u.value={},s.value=void 0,i.clearTimeout(f.value),a.value=v)};c(),b.tryOnScopeDispose(c);const d=()=>{const v=Ul(e,o,l),S=new Worker(v);return S._url=v,S.onmessage=p=>{const{resolve:y=()=>{},reject:w=()=>{}}=u.value,[g,O]=p.data;switch(g){case"SUCCESS":y(O),c(g);break;default:w(O),c("ERROR");break}},S.onerror=p=>{const{reject:y=()=>{}}=u.value;p.preventDefault(),y(p),c("ERROR")},r&&(f.value=setTimeout(()=>c("TIMEOUT_EXPIRED"),r)),S},h=(...v)=>new Promise((S,p)=>{var y;u.value={resolve:S,reject:p},(y=s.value)==null||y.postMessage([[...v]]),a.value="RUNNING"});return{workerFn:(...v)=>a.value==="RUNNING"?(console.error("[useWebWorkerFn] You can only run one instance of the worker at a time."),Promise.reject()):(s.value=d(),h(...v)),workerStatus:a,workerTerminate:c}}function Bl(e={}){const{window:t=L}=e;if(!t)return n.shallowRef(!1);const o=n.shallowRef(t.document.hasFocus()),l={passive:!0};return k(t,"blur",()=>{o.value=!1},l),k(t,"focus",()=>{o.value=!0},l),o}function jl(e={}){const{window:t=L,...o}=e;return Oe(t,o)}function zl(e={}){const{window:t=L,initialWidth:o=Number.POSITIVE_INFINITY,initialHeight:l=Number.POSITIVE_INFINITY,listenOrientation:r=!0,includeScrollbar:i=!0,type:s="inner"}=e,a=n.shallowRef(o),u=n.shallowRef(l),f=()=>{if(t)if(s==="outer")a.value=t.outerWidth,u.value=t.outerHeight;else if(s==="visual"&&t.visualViewport){const{width:d,height:h,scale:m}=t.visualViewport;a.value=Math.round(d*m),u.value=Math.round(h*m)}else i?(a.value=t.innerWidth,u.value=t.innerHeight):(a.value=t.document.documentElement.clientWidth,u.value=t.document.documentElement.clientHeight)};f(),b.tryOnMounted(f);const c={passive:!0};if(k("resize",f,c),t&&s==="visual"&&t.visualViewport&&k(t.visualViewport,"resize",f,c),r){const d=$("(orientation: portrait)");n.watch(d,()=>f())}return{width:a,height:u}}R.DefaultMagicKeysAliasMap=tt,R.StorageSerializers=Re,R.TransitionPresets=Tl,R.asyncComputed=Ae,R.breakpointsAntDesign=an,R.breakpointsBootstrapV5=nn,R.breakpointsElement=fn,R.breakpointsMasterCss=un,R.breakpointsPrimeFlex=cn,R.breakpointsQuasar=rn,R.breakpointsSematic=sn,R.breakpointsTailwind=tn,R.breakpointsVuetify=ln,R.breakpointsVuetifyV2=xe,R.breakpointsVuetifyV3=on,R.cloneFnJSON=ne,R.computedAsync=Ae,R.computedInject=Vt,R.createFetch=Gn,R.createReusableTemplate=Ft,R.createTemplatePromise=Dt,R.createUnrefFn=Ct,R.customStorageEventName=Ee,R.defaultDocument=q,R.defaultLocation=At,R.defaultNavigator=G,R.defaultWindow=L,R.executeTransition=ht,R.formatTimeAgo=mt,R.formatTimeAgoIntl=gl,R.formatTimeAgoIntlParts=Pe,R.getSSRHandler=de,R.mapGamepadToXbox360Controller=ro,R.onClickOutside=Mt,R.onElementRemoval=we,R.onKeyDown=Lt,R.onKeyPressed=Nt,R.onKeyStroke=se,R.onKeyUp=xt,R.onLongPress=Ut,R.onStartTyping=jt,R.provideSSRWidth=en,R.setSSRHandler=bn,R.templateRef=zt,R.unrefElement=W,R.useActiveElement=Le,R.useAnimate=qt,R.useAsyncQueue=Gt,R.useAsyncState=Ne,R.useBase64=Kt,R.useBattery=Qt,R.useBluetooth=Zt,R.useBreakpoints=dn,R.useBroadcastChannel=mn,R.useBrowserLocation=vn,R.useCached=pn,R.useClipboard=hn,R.useClipboardItems=yn,R.useCloned=wn,R.useColorMode=Be,R.useConfirmDialog=Rn,R.useCountdown=En,R.useCssVar=oe,R.useCurrentElement=je,R.useCycleList=Tn,R.useDark=On,R.useDebouncedRefHistory=Fn,R.useDeviceMotion=Pn,R.useDeviceOrientation=Ge,R.useDevicePixelRatio=Dn,R.useDevicesList=Cn,R.useDisplayMedia=An,R.useDocumentVisibility=Ye,R.useDraggable=Mn,R.useDropZone=In,R.useElementBounding=Ln,R.useElementByPoint=Nn,R.useElementHover=xn,R.useElementSize=Xe,R.useElementVisibility=Je,R.useEventBus=Wn,R.useEventListener=k,R.useEventSource=Un,R.useEyeDropper=$n,R.useFavicon=Bn,R.useFetch=Qe,R.useFileDialog=Jn,R.useFileSystemAccess=Qn,R.useFocus=Zn,R.useFocusWithin=oo,R.useFps=lo,R.useFullscreen=ao,R.useGamepad=io,R.useGeolocation=so,R.useIdle=fo,R.useImage=vo,R.useInfiniteScroll=po,R.useIntersectionObserver=Ke,R.useKeyModifier=yo,R.useLocalStorage=wo,R.useMagicKeys=go,R.useManualRefHistory=qe,R.useMediaControls=Ro,R.useMediaQuery=$,R.useMemoize=Eo,R.useMemory=To,R.useMounted=Ie,R.useMouse=nt,R.useMouseInElement=ot,R.useMousePressed=ko,R.useMutationObserver=X,R.useNavigatorLanguage=_o,R.useNetwork=lt,R.useNow=Ve,R.useObjectUrl=Vo,R.useOffsetPagination=Fo,R.useOnline=Po,R.usePageLeave=Do,R.useParallax=Co,R.useParentElement=Ao,R.usePerformanceObserver=Mo,R.usePermission=ue,R.usePointer=Lo,R.usePointerLock=No,R.usePointerSwipe=xo,R.usePreferredColorScheme=Wo,R.usePreferredContrast=Ho,R.usePreferredDark=Ue,R.usePreferredLanguages=Uo,R.usePreferredReducedMotion=$o,R.usePreferredReducedTransparency=Bo,R.usePrevious=jo,R.useRafFn=K,R.useRefHistory=Te,R.useResizeObserver=le,R.useSSRWidth=Se,R.useScreenOrientation=rt,R.useScreenSafeArea=zo,R.useScriptTag=qo,R.useScroll=Oe,R.useScrollLock=Yo,R.useSessionStorage=Xo,R.useShare=Ko,R.useSorted=Qo,R.useSpeechRecognition=Zo,R.useSpeechSynthesis=el,R.useStepper=tl,R.useStorage=me,R.useStorageAsync=nl,R.useStyleTag=ll,R.useSupported=H,R.useSwipe=al,R.useTemplateRefsList=rl,R.useTextDirection=il,R.useTextSelection=ul,R.useTextareaAutosize=fl,R.useThrottledRefHistory=dl,R.useTimeAgo=hl,R.useTimeAgoIntl=wl,R.useTimeoutPoll=bl,R.useTimestamp=Sl,R.useTitle=Rl,R.useTransition=kl,R.useUrlSearchParams=_l,R.useUserMedia=Vl,R.useVModel=yt,R.useVModels=Fl,R.useVibrate=Pl,R.useVirtualList=Dl,R.useWakeLock=Il,R.useWebNotification=Ll,R.useWebSocket=Nl,R.useWebWorker=xl,R.useWebWorkerFn=$l,R.useWindowFocus=Bl,R.useWindowScroll=jl,R.useWindowSize=zl,Object.keys(b).forEach(function(e){e!=="default"&&!Object.prototype.hasOwnProperty.call(R,e)&&Object.defineProperty(R,e,{enumerable:!0,get:function(){return b[e]}})})})(this.VueUse=this.VueUse||{},VueUse,Vue);
